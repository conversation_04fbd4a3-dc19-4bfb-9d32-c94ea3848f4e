"use strict";
import { deviceConnectService } from "../../service/debug/deviceconnect";
import { deviceInfoService } from "../../service/debug/deviceinfo";

import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleErrorResponse,
  handleConnectionError,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { IECReq } from "../../interface/debug/request";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import fs from "fs";
import path from "path";

/**
 * 装置基本信息Controller
 * <AUTHOR>
 * @class
 */
class DeviceInfoController {
  constructor() {
    logger.info(
      `[DeviceInfoController] ${t("logs.deviceInfoController.initialized")}`
    );
  }

  /**
   * 获取装置信息
   * getDeviceInfo
   */
  async getDeviceInfo(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceInfoController] ${t("logs.deviceInfoController.getDeviceInfoStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceInfoController] ${t("logs.deviceInfoController.getDeviceInfoCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceInfoController] ${t("logs.deviceInfoController.getDeviceInfoNotConnected")}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceInfoController] ${t("logs.deviceInfoController.getDeviceInfoConnected")}`
      );
      // 返回列表
      const result = await deviceInfoService.getDeviceInfo(req);

      logger.info(
        `[DeviceInfoController] ${t("logs.deviceInfoController.getDeviceInfoSuccess")}: ${result?.length || 0}`
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceInfoController] ${t("logs.deviceInfoController.getDeviceInfoException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 导出装置信息到Excel文件中，入参包含导出路径，和导出数据列表
   * exportDeviceInfo
   * @param exportPath - 导出文件的路径
   * @param data - 导出的数据列表
   */
  async exportDeviceInfo(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoNotConnected")}`
        );
        return handleConnectionError();
      }

      // 检查数据是否为空
      const { data, selectPath } = req.data;
      logger.debug(
        `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoValidateParams")}: ${data?.length || 0}, ${t("logs.deviceInfoController.exportDeviceInfoPath")}: ${selectPath}`
      );

      if (!data || data.length === 0) {
        logger.warn(
          `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoEmptyData")}`
        );
        return handleCustomResponse(
          ERROR_CODES.NO_DATA,
          ERROR_MESSAGES.NO_DATA
        );
      }

      // 检查导出路径是否为空
      if (!selectPath) {
        logger.error(
          `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoEmptyPath")}`
        );
        return handleCustomResponse(
          ERROR_CODES.INVALID_PARAM,
          t("errors.exportFilePathEmpty")
        );
      }

      // 检查文件扩展名
      const fileExtension = path.extname(selectPath).toLowerCase();
      logger.debug(
        `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoFileExtension")}: ${fileExtension}`
      );

      if (fileExtension !== ".xlsx" && fileExtension !== ".docx") {
        logger.error(
          `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoUnsupportedFormat")}: ${fileExtension}`
        );
        return handleCustomResponse(
          ERROR_CODES.INVALID_PARAM,
          t("errors.exportFileExtensionError")
        );
      }

      const dirPath = path.dirname(selectPath);
      logger.debug(
        `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoDirPath")}: ${dirPath}`
      );

      // 确保目录存在，如果不存在则创建目录
      if (!fs.existsSync(dirPath)) {
        try {
          logger.info(
            `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoCreateDir")}: ${dirPath}`
          );
          fs.mkdirSync(dirPath, { recursive: true });
        } catch (error) {
          logger.error(
            `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoCreateDirFailed")}: ${dirPath}`,
            error
          );
          return handleCustomResponse(
            ERROR_CODES.INVALID_PARAM,
            t("errors.exportFolderCreateFailed")
          );
        }
      }

      // 提取目录路径
      logger.debug(
        `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoCallService")}`
      );
      const result = await deviceInfoService.exportDeviceInfo(req);

      logger.info(
        `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoSuccess")}: ${selectPath}`
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceInfoController] ${t("logs.deviceInfoController.exportDeviceInfoException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }
}

DeviceInfoController.toString = () => "[class DeviceInfoController]";
export default DeviceInfoController;
