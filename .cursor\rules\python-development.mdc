---
description:
globs:
alwaysApply: false
---
# Python 开发规范

## 技术栈
- **语言**: Python
- **Web框架**: FastAPI
- **依赖管理**: pip + requirements.txt
- **构建**: setup.py

## 项目结构
- [python/main.py](mdc:python/main.py) - 主应用入口
- [python/fastapi-demo.py](mdc:python/fastapi-demo.py) - FastAPI示例
- [python/requirements.txt](mdc:python/requirements.txt) - 依赖列表
- [python/setup.py](mdc:python/setup.py) - 安装配置

## 开发命令
```bash
npm run dev-python     # 开发模式
npm run build-python   # 构建Python应用
```

## 开发原则
1. **类型安全** - 使用Python类型注解
2. **异步优先** - 充分利用FastAPI的异步特性
3. **文档驱动** - 自动生成API文档
4. **依赖管理** - 明确的依赖版本控制
5. **测试覆盖** - 编写单元测试和集成测试

## 代码规范
- 遵循PEP 8代码风格
- 使用类型注解提高代码可读性
- 函数和类名使用snake_case
- 常量使用UPPER_CASE
- 使用docstring文档化函数和类

## FastAPI开发规范
- 使用Pydantic模型进行数据验证
- 实现适当的错误处理中间件
- 使用依赖注入管理服务
- 实现API版本控制
- 添加适当的日志记录

## API设计规范
- RESTful API设计原则
- 统一的响应格式
- 使用OpenAPI规范
- 实现适当的认证和授权
- 支持CORS跨域请求

## 部署规范
- 使用虚拟环境隔离依赖
- 支持Docker容器化部署
- 实现健康检查接口
- 配置适当的日志级别
- 支持环境变量配置
