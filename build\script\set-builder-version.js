const fs = require("fs");
const path = require("path");

const builderPath = path.join(__dirname, "../../cmd/builder.json");
const backupPath = builderPath + ".bak";
const pkgPath = path.join(__dirname, "../../package.json");

function getNowTimeStr() {
  const now = new Date();
  const pad = (n) => n.toString().padStart(2, "0");
  return `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(
    now.getDate()
  )}${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
}

function updateBuilderJson() {
  if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(builderPath, backupPath);
  }
  const builder = JSON.parse(fs.readFileSync(builderPath, "utf-8"));
  const pkg = JSON.parse(fs.readFileSync(pkgPath, "utf-8"));
  const version = pkg.version;
  const time = getNowTimeStr();
  // 替换 artifactName 中的 version 和 time
  if (builder.win && builder.win.artifactName) {
    // 直接替换模板变量
    builder.win.artifactName = builder.win.artifactName
      .replace(/\$\{version\}/g, version)
      .replace(/\$\{time\}/g, time);
  }
  fs.writeFileSync(builderPath, JSON.stringify(builder, null, 2), "utf-8");
}

function restoreBuilderJson() {
  if (fs.existsSync(backupPath)) {
    fs.copyFileSync(backupPath, builderPath);
    fs.unlinkSync(backupPath);
  }
}

if (process.argv.includes("--restore")) {
  restoreBuilderJson();
} else {
  updateBuilderJson();
}
