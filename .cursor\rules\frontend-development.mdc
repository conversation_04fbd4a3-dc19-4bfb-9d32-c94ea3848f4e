---
description:
globs:
alwaysApply: false
---
# 前端开发规范

## 技术栈
- **框架**: Vue.js 3 + TypeScript
- **构建工具**: Vite
- **样式**: UnoCSS
- **代码规范**: ESLint + Prettier + Stylelint
- **提交规范**: Commitlint

## 项目结构
- [frontend/src/](mdc:frontend/src/) - 源代码目录
- [frontend/public/](mdc:frontend/public/) - 静态资源
- [frontend/dist/](mdc:frontend/dist/) - 构建输出
- [frontend/build/](mdc:frontend/build/) - 构建配置

## 配置文件
- [frontend/vite.config.ts](mdc:frontend/vite.config.ts) - Vite配置
- [frontend/tsconfig.json](mdc:frontend/tsconfig.json) - TypeScript配置
- [frontend/uno.config.ts](mdc:frontend/uno.config.ts) - UnoCSS配置
- [frontend/.eslintrc.cjs](mdc:frontend/.eslintrc.cjs) - ESLint配置
- [frontend/.prettierrc.cjs](mdc:frontend/.prettierrc.cjs) - Prettier配置

## 开发命令
```bash
npm run dev-frontend    # 开发模式
npm run build-frontend  # 构建前端
```

## 开发原则
1. **组件化开发** - 将UI拆分为可复用的组件
2. **类型安全** - 充分利用TypeScript类型系统
3. **响应式设计** - 确保在不同设备上的良好体验
4. **性能优化** - 使用Vite的快速热重载和构建优化
5. **代码质量** - 遵循ESLint和Prettier规范

## 组件开发规范
- 使用Composition API
- 组件名使用PascalCase
- Props使用TypeScript接口定义
- 事件使用emit定义
- 样式使用UnoCSS原子化CSS
