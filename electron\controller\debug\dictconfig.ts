"use strict";

import { deviceConnectService } from "../../service/debug/deviceconnect";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleConnectionError,
  handleErrorResponse,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { dictConfigService } from "../../service/debug/dictCfg";
import { IECReq } from "../../interface/debug/request";
import { ProjectDictRequestData, SetProjectDictRequestData } from "iec-upadrpc";

/**
 * 词条配置Controller
 * <AUTHOR>
 * @class
 */
class DictConfigController {
  constructor() {
    logger.info(
      `[DictConfigController] ${t("logs.dictConfigController.initialized")}`
    );
  }

  /**
   * 获取工程词条
   * @returns {Promise<ApiResponse>}
   */
  async getProjectDict(req: IECReq<ProjectDictRequestData>): Promise<ApiResponse> {
    logger.info(
      `[DictConfigController] ${t("logs.dictConfigController.getProjectDictStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DictConfigController] ${t("logs.dictConfigController.getProjectDictCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DictConfigController] ${t("logs.dictConfigController.getProjectDictNotConnected")}`
        );
        return handleConnectionError();
      }

      // 获取工程词条
      logger.debug(
        `[DictConfigController] ${t("logs.dictConfigController.getProjectDictConnected")}`
      );
      const dictData = await dictConfigService.getProjectDict(req);

      logger.info(
        `[DictConfigController] ${t("logs.dictConfigController.getProjectDictSuccess")}:`,
        dictData
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        dictData
      );
    } catch (error) {
      logger.error(
        `[DictConfigController] ${t("logs.dictConfigController.getProjectDictException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 设置工程词条
   * @returns {Promise<ApiResponse>}
   */
  async setProjectDict(req: IECReq<SetProjectDictRequestData>): Promise<ApiResponse> {
    logger.info(
      `[DictConfigController] ${t("logs.dictConfigController.setProjectDictStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DictConfigController] ${t("logs.dictConfigController.setProjectDictCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DictConfigController] ${t("logs.dictConfigController.setProjectDictNotConnected")}`
        );
        return handleConnectionError();
      }

      // 设置工程词条
      logger.debug(
        `[DictConfigController] ${t("logs.dictConfigController.setProjectDictConnected")}`
      );
      const result = await dictConfigService.setProjectDict(req);

      logger.info(
        `[DictConfigController] ${t("logs.dictConfigController.setProjectDictSuccess")}:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DictConfigController] ${t("logs.dictConfigController.setProjectDictException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }
}

DictConfigController.toString = () => "[class DictConfigController]";
export default DictConfigController;
