/**
 * 语言控制器
 * 处理前端语言设置相关的IPC请求
 * <AUTHOR> Assistant
 */

import { logger } from "ee-core/log";
import { languageService } from "../../service/common/language";
import { ApiResponse } from "../../data/debug/apiResponse";

/**
 * 语言控制器类
 */
class LanguageController {

  /**
   * 设置语言
   * @param req 请求参数，包含language字段
   * @returns API响应
   */
  async setLanguage(req: { language: string }): Promise<ApiResponse> {
    logger.info("[LanguageController] setLanguage called with:", JSON.stringify(req));
    try {
      const result = await languageService.setLanguage(req.language);
      logger.info("[LanguageController] setLanguage result:", JSON.stringify(result));
      return result;
    } catch (error) {
      logger.error("[LanguageController] setLanguage error:", error);
      throw error;
    }
  }

  /**
   * 获取当前语言
   * @returns API响应，包含当前语言
   */
  async getCurrentLanguage(): Promise<ApiResponse> {
    logger.info("[LanguageController] getCurrentLanguage called");
    try {
      const result = await languageService.getCurrentLanguage();
      logger.info("[LanguageController] getCurrentLanguage result:", JSON.stringify(result));
      return result;
    } catch (error) {
      logger.error("[LanguageController] getCurrentLanguage error:", error);
      throw error;
    }
  }

  /**
   * 同步前端语言设置
   * @param req 请求参数，包含language字段
   * @returns API响应
   */
  async syncLanguage(req: { language: string }): Promise<ApiResponse> {
    logger.info("[LanguageController] syncLanguage called with:", JSON.stringify(req));
    try {
      const result = await languageService.syncLanguageFromFrontend(req);
      logger.info("[LanguageController] syncLanguage result:", JSON.stringify(result));
      return result;
    } catch (error) {
      logger.error("[LanguageController] syncLanguage error:", error);
      throw error;
    }
  }
}

LanguageController.toString = () => '[class LanguageController]';

module.exports = LanguageController;
