import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { SingleGlobalDeviceInfo } from "../../data/debug/singleGlobalDeviceInfo";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
/**
 * <AUTHOR>
 * @version 1.0 2025-04-24
 */
export abstract class BaseController {
  protected getDeviceInfo(id: string): SingleGlobalDeviceInfo | undefined {
    logger.debug(
      `[BaseController] ${t("logs.baseController.getDeviceInfoStart")}: ${id}`
    );

    try {
      const singleGlobalDeviceInfo =
        GlobalDeviceData.getInstance().deviceInfoMap.get(id);

      if (singleGlobalDeviceInfo) {
        logger.debug(
          `[BaseController] ${t("logs.baseController.getDeviceInfoSuccess")}: ${id}`
        );
      } else {
        logger.warn(
          `[BaseController] ${t("logs.baseController.getDeviceInfoNotFound")}: ${id}`
        );
      }

      return singleGlobalDeviceInfo;
    } catch (error) {
      logger.error(
        `[BaseController] ${t("logs.baseController.getDeviceInfoFailed")}: ${id}`,
        error
      );
      return undefined;
    }
  }
}
