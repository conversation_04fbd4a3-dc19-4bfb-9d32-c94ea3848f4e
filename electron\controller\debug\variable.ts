"use strict";

import { deviceConnectService } from "../../service/debug/deviceconnect";
import { variableService } from "../../service/debug/variable";

import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleErrorResponse,
  handleConnectionError,
  handleCustomResponse,
  handleInternalError,
  handleInvalidParam,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { IECReq } from "../../interface/debug/request";

/**
 * 装置变量Controller
 * <AUTHOR>
 * @class
 */
class VariableController {
  // 异步获取所有装置变量，成功返回VariableItem列表，失败返回空列表
  async getVariable(req: IECReq<any>): Promise<ApiResponse> {
    // 获取变量方法入口日志
    logger.info(
      `[VariableController] ${t("logs.variableController.getVariableEntry")}:`,
      JSON.stringify(req)
    );
    if (!(await deviceConnectService.checkConnection(req))) {
      return handleConnectionError();
    }
    try {
      const variable = await variableService.getVariable(req);
      // 获取变量方法返回日志
      logger.info(
        `[VariableController] ${t("logs.variableController.getVariableReturn")}:`,
        JSON.stringify(variable)
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        variable
      );
    } catch (error) {
      // 获取变量方法异常日志
      logger.error(
        `[VariableController] ${t("logs.variableController.getVariableException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  // 异步添加装置变量，失败返回false，成功返回true
  async addVariable(req: IECReq<any>): Promise<ApiResponse> {
    // 添加变量方法入口日志
    logger.info("[VariableController] addVariable 入参:", JSON.stringify(req));
    if (!(await deviceConnectService.checkConnection(req))) {
      return handleConnectionError();
    }
    const param = req.data;
    if (!param || typeof param !== "object" || !param.name) {
      throw handleCustomResponse(
        ERROR_CODES.INVALID_PARAM,
        t("errors.variableNameEmpty")
      );
    }
    try {
      const flag = await variableService.addVariable(req);
      // 添加变量方法返回日志
      logger.info(
        "[VariableController] addVariable 返回:",
        JSON.stringify(flag)
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      // 添加变量方法异常日志
      logger.error("[VariableController] addVariable 异常:", error);
      return handleErrorResponse(error);
    }
  }

  // 异步确认修改变量，修改成功返回true，失败返回false
  async modifyVariable(req: IECReq<any>): Promise<ApiResponse> {
    // 修改变量方法入口日志
    logger.info(
      "[VariableController] modifyVariable 入参:",
      JSON.stringify(req)
    );
    if (!(await deviceConnectService.checkConnection(req))) {
      return handleConnectionError();
    }
    try {
      const flag = await variableService.modifyVariable(req);
      // 修改变量方法返回日志
      logger.info(
        "[VariableController] modifyVariable 返回:",
        JSON.stringify(flag)
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      // 修改变量方法异常日志
      logger.error("[VariableController] modifyVariable 异常:", error);
      return handleErrorResponse(error);
    }
  }

  // 异步删除装置变量，成功返回true，失败返回false
  async deleteVariable(req: IECReq<any>): Promise<ApiResponse> {
    // 删除变量方法入口日志
    logger.info(
      "[VariableController] deleteVariable 入参:",
      JSON.stringify(req)
    );
    if (!(await deviceConnectService.checkConnection(req))) {
      return handleConnectionError();
    }
    try {
      const flag = await variableService.deleteVariable(req);
      // 删除变量方法返回日志
      logger.info(
        "[VariableController] deleteVariable 返回:",
        JSON.stringify(flag)
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      // 删除变量方法异常日志
      logger.error("[VariableController] deleteVariable 异常:", error);
      return handleErrorResponse(error);
    }
  }

  // 异步导出装置变量，导出成功返回true，导出失败返回false
  async exportVariable(req: IECReq<any>): Promise<ApiResponse> {
    // 导出变量方法入口日志
    logger.info(
      "[VariableController] exportVariable 入参:",
      JSON.stringify(req)
    );
    if (!(await deviceConnectService.checkConnection(req))) {
      return handleConnectionError();
    }
    const { path } = req.data;
    // 检查导出路径是否为空
    if (!path) {
      logger.error(
        `[VariableController] ${t("logs.variableController.exportVariableEmptyPath")}`
      );
      return handleCustomResponse(
        ERROR_CODES.INVALID_PARAM,
        t("errors.exportPathEmpty")
      );
    }
    try {
      const flag = await variableService.exportVariable(req);
      // 导出变量方法返回日志
      logger.info(
        "[VariableController] exportVariable 返回:",
        JSON.stringify(flag)
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      // 导出变量方法异常日志
      logger.error("[VariableController] exportVariable 异常:", error);
      return handleErrorResponse(error);
    }
  }

  // 异步导入装置变量，导入成功返回true，导入失败返回false
  async importVariable(req: IECReq<any>): Promise<ApiResponse> {
    // 导入变量方法入口日志
    logger.info(
      "[VariableController] importVariable 入参:",
      JSON.stringify(req)
    );
    if (!(await deviceConnectService.checkConnection(req))) {
      return handleConnectionError();
    }
    const { path } = req.data;
    // 检查导入路径是否为空
    if (!path) {
      logger.error(
        `[VariableController] ${t("logs.variableController.importVariableEmptyPath")}`
      );
      return handleCustomResponse(
        ERROR_CODES.INVALID_PARAM,
        t("errors.importPathEmpty")
      );
    }
    try {
      const flag = await variableService.importVariable(req);
      // 导入变量方法返回日志
      logger.info(
        "[VariableController] importVariable 返回:",
        JSON.stringify(flag)
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      // 导入变量方法异常日志
      logger.error("[VariableController] importVariable 异常:", error);
      return handleErrorResponse(error);
    }
  }
}

VariableController.toString = () => "[class VariableController]";
export default VariableController;
