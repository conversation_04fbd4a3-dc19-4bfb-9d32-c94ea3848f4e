// PaginationImpl.ts

import { Pagination } from "../../interface/debug/pagination";

/**
 * 实现分页数据的类
 * <AUTHOR>
 */
export class ApiPagination<T> implements Pagination<T> {
  total: number; // 总记录数
  pageSize: number; // 每页显示的记录数
  currentPage: number; // 当前页码
  totalPage: number; // 总页数
  list: T[]; // 当前页的数据列表

  constructor(total: number, pageSize: number, currentPage: number, list: T[]) {
    this.total = total;
    this.pageSize = pageSize;
    this.currentPage = currentPage;
    this.list = list;
    this.totalPage = Math.ceil(total / pageSize);
  }

  /**
   * 获取下一页的数据
   * @returns {ApiPagination<T> | null} 下一页的分页数据，如果没有下一页则返回 null
   */
  getNextPage(): ApiPagination<T> | null {
    if (this.currentPage < this.totalPage) {
      // 假设有一个方法来获取下一页的数据
      const nextList = this.fetchNextPageData(this.currentPage + 1);
      return new ApiPagination(
        this.total,
        this.pageSize,
        this.currentPage + 1,
        nextList
      );
    }
    return null;
  }

  /**
   * 获取上一页的数据
   * @returns {ApiPagination<T> | null} 上一页的分页数据，如果没有上一页则返回 null
   */
  getPreviousPage(): ApiPagination<T> | null {
    if (this.currentPage > 1) {
      // 假设有一个方法来获取上一页的数据
      const prevList = this.fetchPreviousPageData(this.currentPage - 1);
      return new ApiPagination(
        this.total,
        this.pageSize,
        this.currentPage - 1,
        prevList
      );
    }
    return null;
  }

  /**
   * 模拟获取下一页的数据
   * @param page 页码
   * @returns {T[]} 数据列表
   */
  private fetchNextPageData(page: number): T[] {
    console.log(page);
    // 这里可以添加实际的数据获取逻辑
    return [];
  }

  /**
   * 模拟获取上一页的数据
   * @param page 页码
   * @returns {T[]} 数据列表
   */
  private fetchPreviousPageData(page: number): T[] {
    console.log(page);
    // 这里可以添加实际的数据获取逻辑
    return [];
  }
}
