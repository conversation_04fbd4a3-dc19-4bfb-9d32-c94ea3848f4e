/**
 * Variable related - English
 */
export default {
  registerFailed: "Variable registration failed, reason:",
  modifyFailed: "Variable modification failed, reason:",
  deleteFailed: "Variable deletion failed, reason:",
  modifyError: "Variable modification failed",
  deleteError: "Variable deletion failed",
  debugVariables: "Device debug variables",
  variableNameExists: "Variable name {name} already exists!",
  variableNameExistsImport: "Variable name {name} already exists!",
  importKeyMapping: {
    variableName: "Variable Name"
  },
  headers: {
    index: "Index",
    name: "Variable Name",
    description: "Variable Description",
    type: "Variable Type",
    value: "Variable Value"
  }
};
