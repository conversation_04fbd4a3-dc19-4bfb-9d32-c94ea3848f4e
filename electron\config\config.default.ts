import path from "path";
import { getBaseDir } from "ee-core/ps";
import { type AppConfig } from "ee-core/config";

const config: () => AppConfig = () => {
  return {
    openDevTools: false,
    singleLock: false,
    windowsOption: {
      title: "SEATool",
      width: 1740,
      height: 830,
      minWidth: 1280,
      minHeight: 700,
      webPreferences: {
        contextIsolation: false,
        nodeIntegration: true,
      },
      frame: false,
      show: false,
      movable: true,
      icon: path.join(getBaseDir(), "public", "images", "logo-32.png"),
      transparent: false,
      hasShadow: true,
      thickFrame: true,
    },
    logger: {
      encoding: "utf8", // 文件编码
      level: "INFO", // 等级
      outputJSON: false, // 是否以json格式输出到文件
      buffer: true,
      enablePerformanceTimer: false,
      rotator: "day", // day:按天切割
      appLogName: "app.log", // 业务日志名
      coreLogName: "app-core.log", // 框架核心日志名
      errorLogName: "app-error.log", // 错误日志名
    },
    remote: {
      enable: false,
      url: "http://electron-egg.kaka996.com/",
    },
    socketServer: {
      enable: true,
      port: 7070,
      path: "/socket.io/",
      connectTimeout: 45000,
      pingTimeout: 30000,
      pingInterval: 25000,
      maxHttpBufferSize: 1e8,
      transports: ["polling", "websocket"],
      cors: {
        origin: true,
      },
      channel: "socket-channel",
    },
    httpServer: {
      enable: true,
      https: {
        enable: false,
        key: "/public/ssl/localhost+1.key",
        cert: "/public/ssl/localhost+1.pem",
      },
      host: "127.0.0.1",
      port: 7071,
    },
    mainServer: {
      indexPath: "/public/dist/index.html",
    },
  };
};

export default config;
