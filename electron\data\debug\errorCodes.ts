// errorCodes.ts
import { ApiResponse } from "./apiResponse";
import { t } from "../i18n/i18n";

/**
 * 定义通用错误码错误描述
 * <AUTHOR>
 */
export const ERROR_CODES = {
  SUCCESS: 0,
  DEVICE_NOT_CONNECTED: -1,
  INVALID_PARAM: 101,
  OPERATE_FAILED: 102,
  CONNECTION_EXISTS: 103,
  NO_DATA: 104,
  INTERNAL_ERROR: 501, // 修改为唯一的错误码
};

// 使用国际化函数获取错误信息
export const getErrorMessage = (code: keyof typeof ERROR_CODES): string => {
  const messageKeys = {
    SUCCESS: "errors.success",
    DEVICE_NOT_CONNECTED: "errors.deviceNotConnected",
    INVALID_PARAM: "errors.invalidParam",
    OPERATE_FAILED: "errors.operateFailed",
    NO_DATA: "errors.noData",
    INTERNAL_ERROR: "errors.internalError",
    CONNECTION_EXISTS: "errors.connectionExists",
  };
  return t(messageKeys[code]);
};

// 为了向后兼容，保留 ERROR_MESSAGES 常量
export const ERROR_MESSAGES = {
  get SUCCESS() {
    return getErrorMessage("SUCCESS");
  },
  get DEVICE_NOT_CONNECTED() {
    return getErrorMessage("DEVICE_NOT_CONNECTED");
  },
  get INVALID_PARAM() {
    return getErrorMessage("INVALID_PARAM");
  },
  get OPERATE_FAILED() {
    return getErrorMessage("OPERATE_FAILED");
  },
  get CONNECTION_EXISTS() {
    return getErrorMessage("CONNECTION_EXISTS");
  },
  get NO_DATA() {
    return getErrorMessage("NO_DATA");
  },
  get INTERNAL_ERROR() {
    return getErrorMessage("INTERNAL_ERROR");
  },
};

export function handleInternalError(errorMsg?: string): ApiResponse {
  // 日志由调用方负责打印，这里只拼接错误信息
  let msg = getErrorMessage("INTERNAL_ERROR");
  if (errorMsg) {
    msg += `: ${errorMsg}`;
  }
  return new ApiResponse(ERROR_CODES.INTERNAL_ERROR, msg);
}

export function handleConnectionError(): ApiResponse {
  return new ApiResponse(
    ERROR_CODES.DEVICE_NOT_CONNECTED,
    getErrorMessage("DEVICE_NOT_CONNECTED")
  );
}

export function handleInvalidParam(): ApiResponse {
  return new ApiResponse(
    ERROR_CODES.INVALID_PARAM,
    getErrorMessage("INVALID_PARAM")
  );
}

export function handleCustomResponse(
  code: number,
  msg: string,
  data?: any
): ApiResponse {
  return new ApiResponse(code, msg, data);
}

export function handleErrorResponse(error: any): ApiResponse {
  if (error instanceof Error) {
    return handleCustomResponse(ERROR_CODES.INTERNAL_ERROR, error.message);
  } else {
    return handleInternalError();
  }
}
