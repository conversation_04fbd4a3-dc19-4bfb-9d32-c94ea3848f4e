"use strict";

import { ApiResponse } from "../../data/debug/apiResponse";
import {
    ERROR_CODES,
    ERROR_MESSAGES,
    handleInternalError,
    handleCustomResponse,
  } from "../../data/debug/errorCodes";
import { deviceConnectService } from "../../service/debug/deviceconnect";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import { UpadRpcConnectRes } from "iec-upadrpc/dist/src/upadrpc/UpadRpcDef";
import { IECResult } from "iec-common/dist/data/iecdata";
import { IECReq } from "../../interface/debug/request";

class DeviceConnectController {
  constructor() {
    logger.info(
      `[DeviceConnectController] ${t("logs.deviceConnectController.initialized")}`
    );
  }

  // 连接装置
  async connectDeviceByRpc(data: string): Promise<ApiResponse> {
    logger.info(
      `[DeviceConnectController] ${t("logs.deviceConnectController.connectDeviceStart")}: ${data}`
    );

    try {
      // const connected = await deviceConnectService.checkConnection();
      // 已连接，不再重复连接
      // if (connected) {
      //     return handleCustomResponse(
      //         ERROR_CODES.CONNECTION_EXISTS,
      //         ERROR_MESSAGES.CONNECTION_EXISTS
      //       );
      // }

      logger.debug(
        `[DeviceConnectController] ${t("logs.deviceConnectController.connectDeviceCallService")}`
      );
      const result: IECResult<UpadRpcConnectRes> =
        await deviceConnectService.connectDeviceByRpc(data);

      logger.debug(
        `[DeviceConnectController] ${t("logs.deviceConnectController.connectDeviceServiceResult")}:`,
        result
      );

      // 处理返回的数据
      if (result.isSuccess() && result.data.code == 0) {
        logger.info(
          `[DeviceConnectController] ${t("logs.deviceConnectController.connectDeviceSuccess")}`
        );
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          ERROR_MESSAGES.SUCCESS
        );
      }

      // 获取错误信息，日志输出
      logger.error(
        `[DeviceConnectController] ${t("logs.deviceConnectController.connectDeviceFailed")}:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.OPERATE_FAILED,
        ERROR_MESSAGES.OPERATE_FAILED,
        result.msg
      );
    } catch (error) {
      // 捕获异常，日志输出
      logger.error(
        `[DeviceConnectController] ${t("logs.deviceConnectController.connectDeviceException")}:`,
        error
      );
      return handleInternalError();
    }
  }

  // 断开连接
  async deviceDisconnect(req: IECReq<string>): Promise<ApiResponse> {
    const deviceId = req.data;
    logger.info(
      `[DeviceConnectController] ${t("logs.deviceConnectController.disconnectDeviceStart")}: ${deviceId}`
    );

    try {
      logger.debug(
        `[DeviceConnectController] ${t("logs.deviceConnectController.disconnectDeviceCheckStatus")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      // 已断开，不再重复断开
      if (!connected) {
        logger.warn(
          `[DeviceConnectController] ${t("logs.deviceConnectController.disconnectDeviceAlready")}: ${deviceId}`
        );
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          t("errors.deviceAlreadyConnected")
        );
      }

      logger.debug(
        `[DeviceConnectController] ${t("logs.deviceConnectController.disconnectDeviceCallService")}`
      );
      const res = await deviceConnectService.disconnectDevice(deviceId);

      logger.info(
        `[DeviceConnectController] ${t("logs.deviceConnectController.disconnectDeviceResult")}:`,
        res
      );

      if (res) {
        logger.info(
          `[DeviceConnectController] ${t("logs.deviceConnectController.disconnectDeviceSuccess")}: ${deviceId}`
        );
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          ERROR_MESSAGES.SUCCESS
        );
      }
    } catch (error) {
      logger.error(
        `[DeviceConnectController] ${t("logs.deviceConnectController.disconnectDeviceException")}: ${deviceId}`,
        error
      );
      return handleInternalError();
    }

    logger.warn(
      `[DeviceConnectController] ${t("logs.deviceConnectController.disconnectDeviceFailed")}: ${deviceId}`
    );
    return handleCustomResponse(
      ERROR_CODES.OPERATE_FAILED,
      ERROR_MESSAGES.OPERATE_FAILED
    );
  }

  // 启动报文监视
  async startMessageMonitor(req: IECReq<any>): Promise<ApiResponse> {
    const deviceId = req.head.id;
    logger.info(
      `[DeviceConnectController] ${t("logs.deviceConnectController.startMessageMonitorStart")}: ${deviceId}`
    );

    try {
      // 检查设备是否连接
      const connected = await deviceConnectService.checkConnection(req);
      if (!connected) {
        logger.warn(
          `[DeviceConnectController] ${t("logs.deviceConnectController.deviceNotConnected")}: ${deviceId}`
        );
        return handleCustomResponse(
          ERROR_CODES.DEVICE_NOT_CONNECTED,
          t("errors.deviceNotConnected")
        );
      }

      // 启动报文监视
      const result = await deviceConnectService.startMessageMonitor(deviceId);
      if (result) {
        logger.info(
          `[DeviceConnectController] ${t("logs.deviceConnectController.startMessageMonitorSuccess")}: ${deviceId}`
        );
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          ERROR_MESSAGES.SUCCESS
        );
      }
    } catch (error) {
      logger.error(
        `[DeviceConnectController] ${t("logs.deviceConnectController.startMessageMonitorException")}: ${deviceId}`,
        error
      );
      return handleInternalError();
    }

    return handleCustomResponse(
      ERROR_CODES.OPERATE_FAILED,
      ERROR_MESSAGES.OPERATE_FAILED
    );
  }

  // 停止报文监视
  async stopMessageMonitor(req: IECReq<any>): Promise<ApiResponse> {
    const deviceId = req.head.id;
    logger.info(
      `[DeviceConnectController] ${t("logs.deviceConnectController.stopMessageMonitorStart")}: ${deviceId}`
    );

    try {
      // 停止报文监视
      const result = await deviceConnectService.stopMessageMonitor(deviceId);
      if (result) {
        logger.info(
          `[DeviceConnectController] ${t("logs.deviceConnectController.stopMessageMonitorSuccess")}: ${deviceId}`
        );
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          ERROR_MESSAGES.SUCCESS
        );
      }
    } catch (error) {
      logger.error(
        `[DeviceConnectController] ${t("logs.deviceConnectController.stopMessageMonitorException")}: ${deviceId}`,
        error
      );
      return handleInternalError();
    }

    return handleCustomResponse(
      ERROR_CODES.OPERATE_FAILED,
      ERROR_MESSAGES.OPERATE_FAILED
    );
  }
}

DeviceConnectController.toString = () => '[class DeviceConnectController]';
module.exports = DeviceConnectController;