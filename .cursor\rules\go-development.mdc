---
description:
globs:
alwaysApply: false
---
# Go 开发规范

## 技术栈
- **语言**: Go
- **Web框架**: 标准库 + 自定义路由
- **模块管理**: Go Modules
- **构建**: 支持多平台交叉编译

## 项目结构
- [go/main.go](mdc:go/main.go) - 应用入口
- [go/go.mod](mdc:go/go.mod) - 模块定义
- [go/api/](mdc:go/api/) - API接口
- [go/config/](mdc:go/config/) - 配置管理
- [go/router/](mdc:go/router/) - 路由定义
- [go/demo/](mdc:go/demo/) - 示例代码

## 开发命令
```bash
npm run dev-go        # 开发模式
npm run dev-go-w      # Windows开发模式
npm run build-go-w    # 构建Windows版本
npm run build-go-m    # 构建macOS版本
npm run build-go-l    # 构建Linux版本
```

## 开发原则
1. **简洁性** - 遵循Go语言的设计哲学
2. **性能优先** - 充分利用Go的并发特性
3. **错误处理** - 明确的错误处理机制
4. **模块化** - 清晰的包结构和依赖关系
5. **测试驱动** - 编写单元测试和集成测试

## 代码规范
- 遵循Go官方代码规范
- 使用gofmt进行代码格式化
- 包名使用小写字母
- 导出函数使用大写开头
- 错误处理使用显式返回

## API设计规范
- RESTful API设计
- 统一的错误响应格式
- 使用JSON进行数据交换
- 实现适当的日志记录
- 支持CORS跨域请求

## 构建规范
- 支持多平台交叉编译
- 使用静态链接减少依赖
- 优化二进制文件大小
- 支持版本信息注入
