import { Theme } from "@/hooks/interface";

export const consoleTheme: Record<Theme.ThemeType, { [key: string]: string }> = {
  light: {
    "--console-bg-color": "#ffffff",
    "--console-workbench-bg-color": "#f5f7fa",
    "--console-workbench-text-color": "#303133",
    "--console-border-color": "#e4e7ed",
    "--console-input-bg-color": "#ffffff",
    "--console-input-text-color": "#303133",
    "--console-hover-bg-color": "#f0f2f5"
  },
  inverted: {
    "--console-bg-color": "#191a20",
    "--console-workbench-bg-color": "#262727",
    "--console-workbench-text-color": "#e5eaf3",
    "--console-border-color": "#414243",
    "--console-input-bg-color": "#191a20",
    "--console-input-text-color": "#e5eaf3",
    "--console-hover-bg-color": "#363636"
  },
  dark: {
    "--console-bg-color": "#141414",
    "--console-workbench-bg-color": "#1f1f1f",
    "--console-workbench-text-color": "#e5eaf3",
    "--console-border-color": "#414243",
    "--console-input-bg-color": "#141414",
    "--console-input-text-color": "#e5eaf3",
    "--console-hover-bg-color": "#2a2a2a"
  }
};
