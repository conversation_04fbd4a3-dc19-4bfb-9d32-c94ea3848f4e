﻿;!@Lang2@!UTF-8!
;  9.20 : <PERSON><PERSON> Bolot
;
;
;
;
;
;
;
;
;
;
0
7-Zip
Kyrgyz
Кыргызча
401
OK
Айнуу



&Ооба
&Жок
&Жабуу
Жардам

&Улантуу
440
Ооба &баарына
Жок б&аарына эмес
Токто
Кайта иштетүү
&Фоном
&Алдыңкы планга
&Тыныгуу
Тыныгууда
Сиз чын эле операцияны токтоткуңуз келип жатабы?
500
&Файл
&Оңдоо
&Кейп
&Тандалган
С&ервис
&Жардам
540
&Ачуу
Ичи&нен ачуу
Сырты&нан ачуу
Көрүү
&Редактирлөө
Атын& өзгөртүү
&Буга көчүрүү...
&Буга ордун которуу...
&Өчүрүү
Фа&лды бөлүштүрүү...
Ф&айлдарды кошуу...
Кас&иети
Комме&нтарий
Контролдук жыйынтык
Diff
&Баштык жаратуу
Фай&л жаратуу
Ч&ыгуу
600
Баарын б&өлүү
Бөлүнгөндү алуу
&Бөлүүнү к&аратуу
Бөлүү...
Бөлүнгөндү алуу...
Түзү менен бөлүү
Түзү менен бөлүнгөндү алуу
700
&Чоң белгилер
&Кичине белгилер
Тиз&ме
&Таблица
730
Иргөөсүз
Түз түзүлүш
&2 Панель
&Аспап панелдери
Түпкү баштыкты ачуу
Бир деңгээлге өйдө өтүү
Баштыктардын тарыхы...
Ж&аңыртуу
750
Архиватордун баскычтарынын панели
Стандарттык баскыч панели
Чоң баскычтар
Баскычтагы жазуулар
800
Кантип тандалганга& баштыкты кошуу
Чөп кат
900
Оңдоп-түздөмөлөр...
Өндүрүмдүүлүктү тестирлөө
960
&Мазмуну...
Программа &жөнүндө...
1003
Жол
Ат
Кенейтүү
Баштык
Көлөм
Кысылган
Атрибуттар
Жаралган
Ачылган
Өзгөртүлгөн
Үзгүлтүксүз
Комментарий
Шифрланган
Чейин бөлүнгөн
Кийин Бөлүнгөн
Сөздүк
CRC
Түр
Анти
Амал
Система
Файлдык система
Колдонуучу
Топ
Блок
Комментарий
Ээлеген ахывал
Жол
Баштыктар
Файлдар
Версиясы
Том
Көптомдуу
Жылдыруу
Сүрүштүрүү
Блоктор
Томдор

64-bit
Big-endian
Процессор
Физикалык көлөм
Баш аттардын көлөмү
Текшерүү жыйынтык
Мүнөздөмөлөр
Виртуалдык дарек
ID
Кыска ат
Жараткан
Сектордун көлөмү
Түзүлүш
Сүрүштүрүү
Ката
Сыйдыргычтык
Бош
Размер кластера
Белги
Локалдык ат
Провайдер
2100
Оңдоп-түздөмөлөр
Тил
Тил:
Редактор
&Редактор:
&Diff:
2200
Система
7-Zip менен файлдарды бириктирүү:
2301
7-Zipти менюнун контексттик кыртышына жайгаштыруу
Каскадттык контексттик меню
Контексттик менюнун элементтери:
2320
<Баштык>
<Архив>
Архивди ачуу
Таңгагын чечүү
Архивке кошуу...
Тестирлөө
Мында таңгагын чечүү
Буга таңгагын чечүү {0}
Буга кошуу {0}
Кысып жана аны email менен жиберүү...
Буга кысып {0} жана email менен жиберүү
2400
Баштыктар
&Иштөө баштык
&Убактылуу системалык баштык
&Учурдагы
&Тапшыруу:
Котормолуу алып жүрүүчүлөргө гана колдонуу
Убактылуу архивтерге турган ордун көрсөтүңүз.
2500
Оңдоп-түздөмөлөр
".." элементин көрсөтүү
Файлдардын реалдуу иконкаларын көрсөтүү
Системалык менюну көрсөтүү
Курсор жалпы сапка
Бөлгүчтөрдү көрсөтүү
Бир басуу менен ачуу
Белгинин алтернативалык режими
Эстин чоң барактарын колдонуу
2900
7-Zip программа жөнүндө
7-Zip эркин таратылуучу программа.
3000
Эркин эске тутуу жетишсиз
Ката табылган жок
{0} Нерселер бөлүнгөн
'{0}' баштыкты жаратуу ишке ашкан жок
Бул архив үчүн өзгөртүү операциялары колдолбойт.
'{0}' архивтей кылып ачуу ишке ашкан жок
Шифрленген архивди ачуу ишке ашкан жок '{0}'. Купуя-белги туура эмес?
Архивдин колдоо кылынбаган түрлөрү
{0} деген файл бар
'{0}' файлы өзгөртүлгөн.\nСиз аны архивде жаңырткыңыз келип жатабы?
\n'{0}' файлын жаңыртуу ишке ашкан жок
Редакторду иштетүү, ишке ашкан жок
Файл вируска окшош (файлдын аты ырааттуу бош жерди камтыйт).
Баштыктын ичинен операция аткарылбайт, жолдун узактыгынан.
Сиз бир фалды бөлүшүңүз керек
Сиз бир нече файлды бөлүшүңүз керек
Өтө көп элементтерди камтыйт
3300
Таңгагын чечүү
Компрессиялоо
Тестирлөө
Ачуу...
Сканирлөө...
3400
Чыгаруу
& Буга таңгагын чечүү:
Чыгарып жаткан файлдар үчүн ордун көрсөтүңүз.
3410
Жолдор
То&лук жолдор
&Жолсуз
3420
Кайта жазуу
&Далилдөө менен
Д&алилдөөсүз
Өткө&рүү
Автоматтык түрдө атын өзгөртүү.
Бар файлдардын аттарын автоматтык түрдө өзгөртүү.
3500
Файлды алмаштырууну далилдөө
Баштык иштеп чыгарылган файлды камтыйт.
Бар файлдарды алмаштыруу
кийинки файл менен?
{0} байт
Автоматтык түрдө атын өзгөртүү.
3700
'{0}' файлдагы колдобоочу кысуу амалы.
'{0}' берилүүсүндөгү ката.файл бузук.
'{0}' CRCте ката.файл бузук.
'{0}' шифрленген файлда ката. Туура эмес купуя-белги?
'{0}' шифрленген файл үчүн CRCте ката. Туура эмес купуя-белги?
3800
Купуя-белгини киргизүү
&Купуя-белгини киргизиңиз:
&Купуя-белгини кайталаңыз:
&Купуя-белгини көрсөтүү
Купуя-белгилер бири-бирине туура келбейт
Купуя-белги үчүн латын алфавитиндеги символдорду гана колдонуңуз,сандарды жана атайын (!, #, $, ...) символдорун
Купуя-белги өтө узун
&Купуя-белги
3900
Өттү:
Калды:
Баары:
Ылдамдык:
Көлөм:
Кысуунун даражасы:
Каталар:
Архивтер:
4000
Архивке кошуу
&Архив:
&Өзгөртүүнүн режими:
&Архивдин форматы:
&Кысуунун деңгээли:
&Кысуунун амалы:
Сөздүктүн &көлөмү:
Сөздүн к&өлөмү:
Блоктун көлөмү:
Агымдардын саны:
&Параметрлер:
&Оңдоп-түздөмөлөр
SF&X-архивди жаратуу
Файлдарды жаздыруу үчүн ачууну кысуу
Шифрлөө
Шифрлөөнүн амалы:
&Файлдардын атын шифрлөө
Салып бекитүү үчүн эстин көлөмү:
Таңгагын чечүү үчүн эстин көломү:
4050
Кысуусуз
Ылдамдуу
Бат
Кадимкидей
Ээң жогорку
Ультра
4060
Кошуп жана алмаштыруу
Жаңыртып жана кошуу
Жаңыртуу
Калптандыруу
4070
Барактоо
Баардык файлдар
Файлдын көлөмүндөй
Үзгүлтүксүз
6000
Көчүрүү
Которуштуруу
Буга көчүрүү:
Буга которуштуруу:
Көчүрүү...
Ордун которуу...
Атын өзгөртүү...
Баштыкты көрсөтүңүз.
Операция бул баштык үчүн колдолбойт.
Баштыктын же файлдын атын өзгөртүүдө ката
Файлдарды көчүрүүнү аныктоо
Сиз бул файлдарды архивке көчүрүүнү чын эле каалап жатасызбы
6100
Файлдын өчүрүүсүн аныктоо
Баштыктын өчүрүүсүн аныктоо
Файлдын тобун өчүрүүсүн аныктоо
Сиз "{0}" өчүрүүнү чын эле каалап жатасызбы?
Сиз "{0}" баштыктын ичиндегилери менен өчүрүүнү чын эле каалап жатасызбы?
({0} даана.) нерселерди өчүрүүнү чын эле каалап жатасызбы?
Өчүрүү...
Баштыкты же файлды өчүрүүдө ката
Система узун жолдуу файлдарды өчүрүүбаштыкка өчүрүү операциясын колдобойт
6300
Баштык жаратуу
Файл жаратуу
Баштыктын аты:
Файлдын аты:
Жаңы баштык
Жаңы файл
Баштык жаратуудан ката
Файл жаратуудан ката
6400
Комментарий
&Комментарий:
Бөлүү
Бөлүнгөндү алып салуу
Маска:
6600
Касиет
Баштыктардын тарыхы
Билдирүү
Билдирүү
7100
Компьютер
Желе
Иш кагаздар
Система
7200
Кошуу
Чыгаруу
Тестирлөө
Көчүрүү
Ордун которуу
Өчүрүү
Маалымат
7300
Файлды бөлүштүрүү
&Буга бөлүштүрүү:
(байт ) көлөмү менен томдорго бөлүштүрүү:
Бөлүштүрүү...
Бөлүштүрүүнү аныктоо
Сиз файлды {0} бөлүккө бөлгүңүз келип жатабы?
Томдун көлөмү баштапкы файлдын көлөмүнөн кичине болушу керек
Томдордун көлөмүн берүүдө ката
Отургузулган томдун көлөмү: {0} байт.\nСиз чын эле архивди ушундай томдорго бөлгүңүз келип жатабы?
7400
Файлдарды бириктирүү
&Буга бириктирүү:
Биригүү...
Бөлүштүрүлгөн файлдын биринчи бөлүгүн гана бөлүү керек
Бөлүштүрүлгөн файлды таану ишке ашкан жок
Бөлүштүрүлгөн файлдын бир бөлүгүн дагы табуу ишке ашкан жок
7500
Текшерүүнүн жыйынтыгын эсептөө...
Текшерүүнүн жыйынтыгы
Маалымат үчүн CRC текшерүү жыйынтыгы:
Маалымат жана аттар үчүн CRC текшерүү жыйынтыгы:
7600
Өндүрүмдүүлүктү тестирлөө
Эстин көлөмү:
Салып бекитүү
Таңгагын чечүү
Рейтинг
Жалпы рейтинг
Учурдагы
Жыйынтыгы
Жүк
Рейтинг / Жүк.
Өтүүлөр:
