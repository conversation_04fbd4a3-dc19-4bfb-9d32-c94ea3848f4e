﻿;!@Lang2@!UTF-8!
;                    : <PERSON><PERSON><PERSON>
;                    : <PERSON><PERSON><PERSON>
;                    : <PERSON><PERSON><PERSON>
;                    : <PERSON><PERSON>
; 22.00 : 2022-06-19 : <PERSON><PERSON>
;
;
;
;
;
;
0
7-Zip
Ukrainian
Українська
401
OK
Скасувати



&Так
&Ні
&Закрити
Довідка

&Продовжити
440
Так для &всіх
Ні для вс&іх
Зупинити
Перезапустити
&На задньому плані
&На передньому плані
&Пауза
Призупинено
Ви впевнені, що бажаєте скасувати операцію?
500
&Файл
&Редагування
&Вигляд
&Уподобання
&Інструменти
&Допомога
540
&Відкрити
Відкр<PERSON><PERSON>и в&середи<PERSON><PERSON>
В<PERSON>дкрити &зовні
&Переглянути
&Редагувати
Пере&йменувати
&Копіювати до...
Пере&містити до...
Ви&далити
Роз&бити файл...
Об'&єднати файли...
В&ластивості
Комент&ар
Обчислити контрольну суму
Порівнювач
Створити папку
Створити файл
Ви&хід
Посилання
Ал&ьтернативні потоки
600
Ви&брати все
Зняти вибір
&Інвертувати вибір
Вибрати...
Зняти вибір...
Вибрати за типом
Зняти вибір за типом
700
Вели&кі піктограми
&Дрібні піктограми
&Список
&Таблиця
730
Без сортування
Плоский вигляд
&2 панелі
&Панелі інструментів
Відкрити кореневу папку
Вище на один рівень
Історія папок...
&Оновити
Автооновлення
750
Панель архіву
Стандартна панель
Великі кнопки
Текст на кнопках
800
&Додати папку до вподобань як
Закладка
900
&Налаштування...
&Тестування продуктивності
960
&Зміст...
&Про 7-Zip...
1003
Шлях
Ім'я
Розширення
Папка
Розмір
Розмір в архіві
Атрибути
Створено
Відкрито
Змінено
Неперервний
З коментарем
Зашифровано
Розбито до
Розбито після
Словник

Тип
Анти
Метод
Походження
Файлова система
Користувач
Група
Блок
Коментар
Позиція
Префікс шляху
Папок
Файлів
Версія
Том
Багатотомний
Зсув
Посилань
Блоків
Частин



Процесор
Фізичний розмір
Розмір заголовків
Контрольна сума
Властивості
Віртуальна адреса

Коротке ім'я
Створено програмою
Розмір сектора
Режим
Посилання
Помилка
Загальний обсяг
Вільний простір
Розмір кластера
Мітка
Локальне ім'я
Провайдер
Безбека NT
Альтернативний потік

Видалено
Дерево


Тип помилки
Помилки
Помилки
Попередження
Попередження
Потоки
Альтернативні потоки
Розмір альтернативних потоків
Віртуальний розмір
Розпакований розмір
Загальний фізичний розмір
Індекс тому
Підтип
Короткий коментар
Кодова сторінка



Розмір залишку
Розмір вбудованої заглушки
Посилання
Жорстке посилання
iNode

Лише для читання

Посилання копіювання


Метадані змінилися
2100
Опції
Мова
Мова:
Редагування
&Редактор:
&Порівнювач:
2200
Система
Асоціювати 7-Zip з:
Усі користувачі
2301
Інтегрувати 7-Zip до контекстного меню оболонки
Каскадне контекстне меню
Пункти контекстного меню:
Піктограми в контекстному меню
2320
<Папка>
<Архів>
Відкрити архів
Видобути файли...
Додати до архіву...
Тестувати архів
Видобути до поточної папки
Видобути до {0}
Додати до {0}
Стиснути та надіслати...
Стиснути до {0} та надіслати
2400
Папки
&Робоча папка
&Системна тимчасова папка
&Поточна
&Задати:
Використовувати тільки для змінних носіїв
Вкажіть розташування тимчасових архівних файлів.
2500
Налаштування
Відображати елемент ".."
Відображати справжні піктограми файлів
Відображати системне меню
Вибір &цілого рядка
Відображати лінії &сітки
Відкривати об'єкти одним кліком
&Альтернативний режим виділення
Використовувати &великі сторінки пам'яті
2900
Про 7-Zip
7-Zip є вільним програмним забезпеченням
3000
Система не може виділити необхідний обсяг пам'яті
Без помилок
Обрано об'єктів: {0}
Не вдається створити папку '{0}'
Операція оновлення не підтримується для даного архіву.
Не вдається відкрити файл '{0}' як архів
Не вдається відкрити зашифрований архів '{0}'. Хибний пароль?
Непідтримуватий тип архіву
Файл {0} вже існує
Файл '{0}' було змінено.\nБажаєте оновити його в архіві?
Неможливо оновити файл\n'{0}'
Не вдається запустити редактор.
Файл виглядає як вірус (ім'я файлу містить довгу послідовність пробілів).
Операцію не можна викликати з папки, яка має довгий шлях.
Ви повинні вибрати один файл
Ви повинні вибрати один або декілька файлів
Забагато елементів
Не вдалося відкрити файл як {0} архів
Файл відкрито як {0} архів
Архів відкрито зі зсувом
3300
Видобування
Стиснення
Тестування
Відкриття...
Сканування...
Видалення
3320
Додавання
Оновлення
Аналіз
Реплікація
Перепакування
Пропуск
Видалення
Створення заголовків
3400
Видобути
В&идобути до:
Вкажіть розташування для видобутих файлів.
3410
Обробка шляхів
Повні шляхи
Без шляхів
Абсолютні шляхи
Відносні шляхи
3420
Режим перезапису
Запитувати перед перезаписом
Перезаписувати без запиту
Пропускати існуючі файли
Автоматично перейменовувати
Автоматично перейменовувати існуючі файли
3430
Усувати дублювання кореневої папки
Відновляти дані безпеки файлу
3440
Поширювати потік Zone.Id:
Для файлів Office
3500
Підтвердіть заміну файлу
Папка призначення вже містить оброблюваний файл.
Бажаєте замінити існуючий файл
на такий?
{0} байт
Перейменовувати &автом.
3700
Непідтривуваний метод стиснення для '{0}'.
Помилка даних у '{0}'. Файл пошкоджено.
Помилка CRC у '{0}'. Файл пошкоджено.
Помилка даних у зашифрованому файлі '{0}'. Хибний пароль?
Помилка CRC у зашифрованому файлі '{0}'. Хибний пароль?
3710
Хибний пароль?
3721
Непідтримуваний метод стиснення
Помилка даних
Помилка CRC
Недоступні дані
Неочікуваний кінець даних
Існують деякі дані після закінчення корисних даних
Не є архівом
Помилка заголовків
Неправильний пароль
3763
Недоступний початок архіву
Непідтверджений початок архіву



Непідтримувана функція
3800
Уведіть пароль
Уведіть пароль:
Повторіть пароль:
&Відображати пароль
Паролі не співпадають
Для паролю використовуйте лише англійські літери, цифри та спеціальні символи (!, #, $, ...)
Пароль занадто довгий
Пароль
3900
Минуло часу:
Залишилося:
Загалом:
Швидкість:
Оброблено:
Ступінь стиснення:
Помилок:
Архівів:
4000
Додати до архіву
&Архів:
&Режим оновлення:
&Формат архіву:
С&тупінь стиснення:
&Метод стискання:
&Розмір словника:
Р&озмір слова:
Розмір блоку:
Кількість потоків:
&Параметри:
Налаштування
&Створити SFX архів
Стискати спільні файли
Шифрування
Метод шифрування:
Шифрувати &імена файлів
Необхідно пам'яті для стискання:
Необхідно пам'яті для видобування:
Видалити файли після стиснення
4040
Зберігати символічні посилання
Зберігати жорсткі посилання
Зберігати альтернативні потоки даних
Зберігати дані безпеки файлу
4050
Без стиснення
Найшвидше
Швидке
Нормальне
Максимальне
Ультра
4060
Додати та замінити файли
Оновити та замінити файли
Оновити існуючі файли
Синхронізувати файли
4070
Переглянути
Усі файли
За розміром файлу
Не неперервний
4080
Час
Точність часової позначки:
Зберігати час модифікації
Зберігати час створення
Зберігати час останнього доступу
Встановити час архіву за часом найновішого файлу
Не змінювати час останнього доступу вихідних файлів
4090
sec
ns
6000
Копіювати
Перемістити
Копіювати до:
Перемістити до:
Копіювання...
Переміщення...
Перейменування...
Виберіть папку призначення.
Операція не підтримується для цієї папки.
Помилка перейменування файлу або папки
Підтвердіть копіювання файлу
Ви впевнені, що хочете скопіювати файли до архіву
6100
Підтвердіть видалення файлу
Підтвердіть видалення папки
Підтвердіть видалення декількох файлів
Ви впевнені, що хочете видалити '{0}'?
Ви впевнені, що хочете видалити папку '{0}' і весь її вміст?
Ви впевнені, що хочете видалити ці елементи ({0} шт.)?
Видалення...
Помилка при видаленні файлу або папки
Системі не вдалося перемістити файл із довгим шляхом до Кошика
6300
Створити папку
Створити файл
Ім'я папки:
Ім'я файлу:
Нова папка
Новий файл
Помилка при створенні папки
Помилка при створенні файлу
6400
Коментар
&Коментар:
Вибрати
Зняти вибір
Маска:
6600
Властивості
Історія папок
Діагностичні повідомлення
Повідомлення
7100
Комп'ютер
Мережа
Документи
Система
7200
Додати
Видобути
Тестувати
Копіювати
Перемістити
Видалити
Інформація
7300
Розбити файл
&Розбити до:
Розбити на &томи розміром, байт:
Розбиття...
Підтвердіть розбиття
Ви впевнені, що бажаєте розбити архів на {0} томів?
Розмір тому має бути меншим за розмір вихідного файлу
Неправильний розмір тому
Задано розмір тому: {0} байт.\nВи впевнені, що бажаєте розбити архів на такі томи?
7400
Об'єднати файли
&Об'єднати до:
Об'єднання...
Виберіть тільки першу частину розбитого файлу
Не вдалося визначити файл, як частину розбитого файлу
Не вдалося знайти більше однієї частини розбитого файлу
7500
Обчислення контрольної суми...
Інформація про контрольну суму
Контрольна сума CRC для даних:
Контрольна сума CRC для даних та імен:
7600
Тестування продуктивності
Використано пам'яті:
Стискання
Видобування
Рейтинг
Загальний рейтинг
Поточні значення
Підсумкові значення
Завант. ЦП
Рейтинг/Завант.
Проходів:
7700
Посилання
Пов'язати
Джерело:
Мета:
7710
Тип посилання
Жорстке посилання
Символічне посилання (файл)
Символічне посилання (каталог)
Точка з'єднання (каталог)
