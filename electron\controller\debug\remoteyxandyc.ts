"use strict";

import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleConnectionError,
  handleCustomResponse,
  handleErrorResponse,
  handleInternalError,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import { IECReq } from "../../interface/debug/request";
import { remoteYxAndYcService } from "../../service/debug/remoteyxandyc";
import { deviceConnectService } from "../../service/debug/deviceconnect";

/**
 * 构造装置菜单树
 * @class wzl
 */
class RemoteYxAndYcController {
  constructor() {}

  async ykycGetGroupData(req: IECReq<any>): Promise<ApiResponse> {
    // 获取遥信遥测分组数据方法入口日志
    logger.info(
      `[RemoteYxAndYcController] ${t("logs.remoteYxAndYcController.ykycGetGroupDataEntry")}:`,
      JSON.stringify(req)
    );
    try {
      const res = await remoteYxAndYcService.ykycGetGroupData(req);
      // 获取遥信遥测分组数据方法返回日志
      logger.info(
        `[RemoteYxAndYcController] ${t("logs.remoteYxAndYcController.ykycGetGroupDataReturn")}`
      );
      return new ApiResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS, res);
    } catch (error) {
      // 获取遥信遥测分组数据方法异常日志
      logger.error(
        `[RemoteYxAndYcController] ${t("logs.remoteYxAndYcController.ykycGetGroupDataException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  // 异步导出遥信遥测，成功返回true，失败返回ApiResponse
  async exportAllData(req: IECReq<any>): Promise<ApiResponse> {
    // 导出遥信遥测方法入口日志
    logger.info(
      `[RemoteYxAndYcController] ${t("logs.remoteYxAndYcController.exportAllDataEntry")}:`,
      JSON.stringify(req)
    );
    if (!(await deviceConnectService.checkConnection(req))) {
      return handleConnectionError();
    }
    const { path } = req.data;
    // 检查导出路径是否为空
    if (!path) {
      logger.error(
        `[RemoteYxAndYcController] ${t("logs.remoteYxAndYcController.exportAllDataEmptyPath")}`
      );
      return handleCustomResponse(
        ERROR_CODES.INVALID_PARAM,
        t("errors.exportPathEmpty")
      );
    }
    try {
      const flag = await remoteYxAndYcService.exportAllVal(req);
      // 导出遥信遥测方法返回日志
      logger.info(
        `[RemoteYxAndYcController] ${t("logs.remoteYxAndYcController.exportAllDataReturn")}:`,
        JSON.stringify(flag)
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      // 导出遥信遥测方法异常日志
      logger.error(
        `[RemoteYxAndYcController] ${t("logs.remoteYxAndYcController.exportAllDataException")}:`,
        error
      );
      return handleInternalError();
    }
  }
}

RemoteYxAndYcController.toString = () => "[class RemoteControlController]";
module.exports = RemoteYxAndYcController;
