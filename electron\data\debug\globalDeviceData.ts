"use strict";

import { SingleGlobalDeviceInfo } from "./singleGlobalDeviceInfo";

/**
 * 全局单例缓存
 * id 确保唯一性
 * wzl
 */
class GlobalDeviceData {

  private static instance: GlobalDeviceData;
  // key 用UUID确认唯一性
  deviceInfoMap: Map<string, SingleGlobalDeviceInfo>;

  constructor() {
    this.deviceInfoMap = new Map();
  }

  getDeviceInfoGlobal(id: string) {
    return this.deviceInfoMap.get(id);
  }

  setDeviceInfoGlobal(id: string, client: SingleGlobalDeviceInfo) {
    this.deviceInfoMap.set(id, client);
  }

  public static getInstance(): GlobalDeviceData {
    if (!GlobalDeviceData.instance) {
      GlobalDeviceData.instance = new GlobalDeviceData();
    }
    return GlobalDeviceData.instance;
  }

  // 获取当前装置连接
  public getClient(id: string) {
    return this.getDeviceInfoGlobal(id)?.deviceClient;
  }
}

GlobalDeviceData.toString = () => "[class GlobalDeviceData]";
export = GlobalDeviceData;
