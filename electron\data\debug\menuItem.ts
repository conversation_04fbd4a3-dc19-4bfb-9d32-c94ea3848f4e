// /**
//  * 菜单描述
//  * <AUTHOR>
//  */
// export const MENU_ITEM = {
//   DEV_INFO: "DEV_INFO",
//   STATE_TABLE: "STATE_TABLE",
//   REPORT_TABLE: "REPORT_TABLE",
//   SETTING: "SETTING",
//   CTRL_TABLE: "CTRL_TABLE",
// };

// export const MENU_TABLE = {
//   DEV_BASIC_TABLE: "DEV_BASIC_TABLE",
//   ACT_TABLE: "ACT_TABLE",
//   YX_TABLE: "YX_TABLE",
//   YC_TABLE: "YC_TABLE",
//   YK_TABLE: "YK_TABLE",
//   DRIVE_TABLE: "DRIVE_TABLE",
//   OPERATE_TABLE: "OPERATE_TABLE",
//   ALLSETTING_TABLE: "ALLSETTING_TABLE",
// };
// export const MENU_DESC = {
//   ACT_TABLE: "动作报告",
//   REPORT_TABLE: "报告",
//   OPERATE_TABLE: "操作报告",
// };
export const MENU_ITEM = {
  SG: "SG",
  SP: "SP",
  ST: "ST",
  MX: "MX",
  CO: "CO",
  BO: "BO",
  ALLSETTING_TABLE: "ALLSETTING_TABLE",
  ALLEDITSETTING_TABLE: "ALLEDITSETTING_TABLE",
  DEV_BASIC_TABLE: "DEV_BASIC_TABLE",
};