/**
 * Virtual device Russian language pack
 */
export default {
  menus: {
    analog: {
      name: "Аналоговый",
      desc: "Аналоговые параметры"
    },
    digitalInput: {
      name: "Цифровой Вход",
      desc: "Параметры цифрового входа"
    },
    digitalOutput: {
      name: "Цифровой Выход",
      desc: "Параметры цифрового выхода"
    },
    fault: {
      name: "Неполадка",
      desc: "Параметры неполадки"
    },
    led: {
      name: "Параметры LED",
      desc: "Параметры LED"
    },
    waveReplay: {
      name: "Воспроизведение Неполадки",
      desc: "Воспроизведение формы волны неполадки"
    }
  },
  items: {
    analog: {
      name: "Аналоговый Вход 1",
      desc: "Аналоговый Вход 1"
    },
    digitalInput: {
      name: "Ц<PERSON><PERSON>ров<PERSON>й Вход 1",
      desc: "Цифровой Вход 1"
    },
    digitalOutput: {
      name: "Цифровой Выход 1",
      desc: "Цифровой Выход 1"
    },
    fault: {
      name: "Сигнал Неполадки 1",
      desc: "Сигнал Неполадки 1"
    },
    led: {
      name: "Яркость LED",
      desc: "Яркость LED"
    },
    waveReplay: {
      name: "Файл Формы Волны",
      desc: "Файл Формы Волны"
    }
  }
};