import path from "node:path";
import IECCONSTANTS from "../../data/debug/iecConstants";
import { GraphDefineService } from "../../service/hmi/GraphDefineService";
import { logger } from "ee-core/log";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleCustomResponse,
  handleErrorResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
/**
 * <AUTHOR>
 * @version 1.0 2025-03-15
 */
class GraphDefineController {
  graphDefineService: GraphDefineService;
  constructor() {
    this.graphDefineService = new GraphDefineService(
      path.join(IECCONSTANTS.PATH_CONFIGURE_EQUIPMENT)
    );
  }
  async get(): Promise<ApiResponse> {
    try {
      logger.info("[GraphDefineController] get - Start", null);
      const res = await this.graphDefineService.getEquipmentList();
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        res
      );
    } catch (error) {
      logger.error("[GraphDefineController] get - Error occurred", error);
      return handleErrorResponse(error);
    }
  }
  async save(data: string): Promise<ApiResponse> {
    try {
      logger.info("[GraphDefineController] save - Start", null);
      const res = await this.graphDefineService.addEquipment(JSON.parse(data));
      return res;
    } catch (error) {
      logger.error("[GraphDefineController] save - Error occurred", error);
      return handleErrorResponse(error);
    }
  }
  async delete(id: string): Promise<ApiResponse> {
    try {
      logger.info("[GraphDefineController] delete - Start", null);
      const res = await this.graphDefineService.RemoveEquipment(id);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        res
      );
    } catch (error) {
      logger.error("[GraphDefineController] delete - Error occurred", error);
      return handleErrorResponse(error);
    }
  }
}
module.exports = GraphDefineController;
