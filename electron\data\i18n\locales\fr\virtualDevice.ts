/**
 * Virtual device French language pack
 */
export default {
  menus: {
    analog: {
      name: "Analogique",
      desc: "Paramètres analogiques"
    },
    digitalInput: {
      name: "<PERSON>tr<PERSON> Numérique",
      desc: "Paramètres d'entrée numérique"
    },
    digitalOutput: {
      name: "Sortie Numérique",
      desc: "Paramètres de sortie numérique"
    },
    fault: {
      name: "Défaut",
      desc: "Paramètres de défaut"
    },
    led: {
      name: "Paramètres LED",
      desc: "Paramètres LED"
    },
    waveReplay: {
      name: "Lecture de Défaut",
      desc: "Lecture de forme d'onde de défaut"
    }
  },
  items: {
    analog: {
      name: "Entrée Analogique 1",
      desc: "Entrée Analogique 1"
    },
    digitalInput: {
      name: "Entrée Numérique 1",
      desc: "Entrée Numérique 1"
    },
    digitalOutput: {
      name: "Sortie Numérique 1",
      desc: "Sortie Numérique 1"
    },
    fault: {
      name: "Signal de Défaut 1",
      desc: "Signal de Défaut 1"
    },
    led: {
      name: "Luminosité LED",
      desc: "Luminosité LED"
    },
    waveReplay: {
      name: "Fichier de Forme d'Onde",
      desc: "Fichier de Forme d'Onde"
    }
  }
};