import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import { IECReq } from "../../interface/debug/request";
import { realEventService } from "../../service/debug/realevent";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { IECResult } from "iec-common/dist/data/iecdata";
import {
  UnSubRealEventRequestData,
  UpadRpcServiceRes,
} from "iec-upadrpc/dist/src/data";
import { BaseController } from "./basecontroller";

/**
 * 事件订阅控制器
 * <AUTHOR>
 * @version 1.0 2025-04-24
 */
class RealEventController extends BaseController {
  constructor() {
    super();
    logger.info(
      `[RealEventController] ${t("logs.realEventController.initialized")}`
    );
  }

  async subRealEvent(req: IECReq<string[]>) {
    logger.info(
      `[RealEventController] ${t("logs.realEventController.subRealEventStart")}:`,
      req
    );

    let res: IECResult<UpadRpcServiceRes>;
    try {
      logger.debug(
        `[RealEventController] ${t("logs.realEventController.subRealEventCallService")}`
      );
      res = await realEventService.subRealEvent(req);
    } catch (e) {
      logger.error(
        `[RealEventController] ${t("logs.realEventController.subRealEventException")}:`,
        e
      );
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        ERROR_MESSAGES.INTERNAL_ERROR
      );
    }

    logger.info(
      `[RealEventController] ${t("logs.realEventController.subRealEventServiceResult")}:`,
      res
    );

    if (!res.isSuccess()) {
      logger.error(
        `[RealEventController] ${t("logs.realEventController.subRealEventFailed")}: ${res.msg || `错误码${res.code}`}`
      );
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        res.msg ? res.msg : `错误码${res.code}`
      );
    }

    if (res.data && res.data.ServiceError != 0) {
      const deviceInfo = this.getDeviceInfo(req.head.id);
      const msg =
        deviceInfo?.getServiceErrMsgByCode(res.data.ServiceError + "") ||
        t("errors.unknownServiceError");
      logger.error(
        `[RealEventController] ${t("logs.realEventController.subRealEventServiceError")}: ${res.data.ServiceError}, ${t("errors.errorInfo")}: ${msg}`
      );
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        `${t("errors.subscribeEventError")}，${msg}`
      );
    }

    logger.info(
      `[RealEventController] ${t("logs.realEventController.subRealEventSuccess")}`
    );
    return handleCustomResponse(
      ERROR_CODES.SUCCESS,
      ERROR_MESSAGES.SUCCESS,
      res
    );
  }

  async unSubRealEvent(req: IECReq<UnSubRealEventRequestData>) {
    logger.info(
      `[RealEventController] ${t("logs.realEventController.unSubRealEventStart")}:`,
      req
    );

    let res: IECResult<UpadRpcServiceRes>;
    try {
      logger.debug(
        `[RealEventController] ${t("logs.realEventController.unSubRealEventCallService")}`
      );
      res = await realEventService.unSubRealEvent(req);
    } catch (e) {
      logger.error(
        `[RealEventController] ${t("logs.realEventController.unSubRealEventException")}:`,
        e
      );
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        ERROR_MESSAGES.INTERNAL_ERROR
      );
    }

    logger.info(
      `[RealEventController] ${t("logs.realEventController.unSubRealEventServiceResult")}:`,
      res
    );

    if (!res.isSuccess()) {
      logger.error(
        `[RealEventController] ${t("logs.realEventController.unSubRealEventFailed")}: ${res.msg || `错误码${res.code}`}`
      );
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        res.msg ? res.msg : `错误码${res.code}`
      );
    }

    if (res.data && res.data.ServiceError != 0) {
      const deviceInfo = this.getDeviceInfo(req.head.id);
      const msg =
        deviceInfo?.getServiceErrMsgByCode(res.data.ServiceError + "") ||
        t("errors.unknownServiceError");
      logger.error(
        `[RealEventController] ${t("logs.realEventController.unSubRealEventServiceError")}: ${res.data.ServiceError}, ${t("errors.errorInfo")}: ${msg}`
      );
      return handleCustomResponse(
        ERROR_CODES.INTERNAL_ERROR,
        `${t("errors.unsubscribeEventError")}，${msg}`
      );
    }

    logger.info(
      `[RealEventController] ${t("logs.realEventController.unSubRealEventSuccess")}`
    );
    return handleCustomResponse(
      ERROR_CODES.SUCCESS,
      ERROR_MESSAGES.SUCCESS,
      res
    );
  }
}

RealEventController.toString = () => "[class RealEventController]";
module.exports = RealEventController;
