---
description: 
globs: 
alwaysApply: false
---
# 代码质量规范

## 核心原则
- **第一性原理** - 从根本原理出发进行架构设计
- **DRY原则** - Don't Repeat Yourself，避免代码重复
- **KISS原则** - Keep It Simple, Stupid，保持简单
- **SOLID原则** - 面向对象设计的五个基本原则
- **YAGNI原则** - You Aren't Gonna Need It，不要过度设计

## 代码分解规则
当单个类、函数或代码文件超过500行时，必须进行识别、分解和分离：
1. **识别** - 分析代码的职责和复杂度
2. **分解** - 按功能模块拆分代码
3. **分离** - 将相关功能提取到独立的模块

## 前端代码规范
- **Vue.js**: 使用Composition API，组件化开发
- **TypeScript**: 充分利用类型系统，定义接口
- **样式**: 使用UnoCSS原子化CSS
- **组件**: 单一职责，可复用，可测试

## 后端代码规范
- **Go**: 遵循官方代码规范，错误处理明确
- **Python**: 遵循PEP 8，使用类型注解
- **API设计**: RESTful设计，统一响应格式
- **错误处理**: 完善的错误处理机制

## 架构设计规范
- **模块化**: 清晰的模块边界和依赖关系
- **分层架构**: 表现层、业务层、数据层分离
- **依赖注入**: 降低模块间耦合度
- **接口设计**: 定义清晰的接口契约

## 测试规范
- **单元测试**: 覆盖核心业务逻辑
- **集成测试**: 验证模块间协作
- **端到端测试**: 验证完整业务流程
- **性能测试**: 确保性能指标达标

## 文档规范
- **代码注释**: 关键逻辑必须有注释
- **API文档**: 自动生成API文档
- **架构文档**: 记录系统设计决策
- **部署文档**: 详细的部署和运维指南

## 版本控制规范
- **提交信息**: 使用规范的提交信息格式
- **分支管理**: 主分支、开发分支、功能分支
- **代码审查**: 重要变更需要代码审查
- **发布流程**: 规范的发布和部署流程
