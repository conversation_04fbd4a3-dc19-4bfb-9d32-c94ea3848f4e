import { DebugInfo, DebugInfoMenu, DebugInfoItem, Header } from "../../interface/debug/debuginfo";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";

/**
 * 虚拟化装置菜单服务
 * 为虚拟化装置提供固定的菜单结构，无需读取debug_info.xml文件
 * <AUTHOR>
 * @class
 */
class VirtualDeviceMenuService {
  constructor() {
    logger.info("[VirtualDeviceMenuService] 虚拟化装置菜单服务初始化完成");
  }

  /**
   * 生成虚拟化装置的固定菜单结构
   * @param deviceId 装置ID
   * @returns DebugInfo 固定的菜单结构
   */
  public getVirtualDeviceMenu(deviceId: string): DebugInfo {
    logger.info(`[VirtualDeviceMenuService] 生成虚拟化装置菜单: ${deviceId}`);

    // 虚拟化装置直接使用扁平化的菜单结构，无需"虚拟化功能"父级菜单
    const virtualMenu: DebugInfo = {
      configVersion: "1.0.0",
      header: {
        updateTime: new Date().toISOString(),
        md5: "virtual_device_menu",
      },
      dataTypeTemplates: {
        EnumType: [],
      },
      menus: [
        {
          name: t("virtualDevice.menus.analog.name"),
          desc: t("virtualDevice.menus.analog.desc"),
          fc: "read_analoy_para",
          method: "read_analoy_para",
          items: [],
          menus: [],
        },
        {
          name: t("virtualDevice.menus.digitalInput.name"),
          desc: t("virtualDevice.menus.digitalInput.desc"),
          fc: "read_bi_para",
          method: "read_bi_para",
          items: [],
          menus: [],
        },
        {
          name: t("virtualDevice.menus.digitalOutput.name"),
          desc: t("virtualDevice.menus.digitalOutput.desc"),
          fc: "read_bo_para",
          method: "read_bo_para",
          items: [],
          menus: [],
        },
        {
          name: t("virtualDevice.menus.fault.name"),
          desc: t("virtualDevice.menus.fault.desc"),
          fc: "read_fault_para",
          method: "read_fault_para",
          items: [],
          menus: [],
        },
        {
          name: t("virtualDevice.menus.led.name"),
          desc: t("virtualDevice.menus.led.desc"),
          fc: "read_led_para",
          method: "read_led_para",
          items: [],
          menus: [],
        },
        {
          name: t("virtualDevice.menus.waveReplay.name"),
          desc: t("virtualDevice.menus.waveReplay.desc"),
          fc: "wave_replay",
          method: "wave_replay",
          items: [],
          menus: [],
        },
      ],
    };

    logger.info(
      `[VirtualDeviceMenuService] 虚拟化装置菜单生成完成: ${deviceId}`
    );
    logger.debug(
      `[VirtualDeviceMenuService] 虚拟化装置菜单详情:`,
      virtualMenu.menus.map((menu) => ({
        name: menu.name,
        desc: menu.desc,
        fc: menu.fc,
        itemsCount: menu.items?.length || 0,
        menusCount: menu.menus?.length || 0,
        items:
          menu.items?.map((item) => ({ name: item.name, desc: item.desc })) ||
          [],
      }))
    );
    return virtualMenu;
  }

  /**
   * 检查装置是否为虚拟化装置
   * @param deviceInfo 装置信息
   * @returns boolean 是否为虚拟化装置
   */
  public isVirtualDevice(deviceInfo: any): boolean {
    return deviceInfo?.isVirtual === true;
  }
}

const virtualDeviceMenuService = new VirtualDeviceMenuService();
export { VirtualDeviceMenuService, virtualDeviceMenuService };