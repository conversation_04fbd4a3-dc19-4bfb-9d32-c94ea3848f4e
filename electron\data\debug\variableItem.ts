/**
 * 使用ts定义模型类，包含字段，序号，变量名称，变量地址, 值
 * <AUTHOR>
 */

// variableItem.ts
export default class VariableItem {
  index: number;
  name: string;
  type: string;
  desc:string;
  addr: string;
  value: string;
  id: string;

  constructor(data: { index: number; desc: string;  id: string; name: string; type: string; addr: string; value: string }) {
    this.index = data.index;
    this.name = data.name;
    this.desc = data.desc;
    this.type = data.type;
    this.addr = data.addr;
    this.value = data.value;
    this.id = data.id;
  }
}
