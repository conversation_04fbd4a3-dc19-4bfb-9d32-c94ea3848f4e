import fs from "fs";
import path from "path";
import { app as electronApp, dialog, shell } from "electron";
import { windowService } from "../../service/os/window";
import { isEmpty } from "lodash";
import { logger } from "ee-core/log";
import {
  getFileOrFolderSize,
  getFileOrFolderSizeAsync,
  getFileOrFolderSizeFast,
} from "../../utils/common";
import { t } from "../../data/i18n/i18n";
/**
 * example
 * @class
 */
class OsController {
  /**
   * All methods receive two parameters
   * @param args Parameters transmitted by the frontend
   * @param event - Event are only available during IPC communication. For details, please refer to the controller documentation
   */

  /**
   * Message prompt dialog box
   */
  async messageShow(): Promise<string> {
    await dialog.showMessageBox({
      type: "info", // "none", "info", "error", "question" 或者 "warning"
      title: t("dialogs.customTitle"),
      message: t("dialogs.customMessage"),
      detail: t("dialogs.additionalInfo"),
    });

    return t("dialogs.messageBoxOpened");
  }

  /**
   * Message prompt and confirmation dialog box
   */
  async messageShowConfirm(): Promise<string> {
    const res = await dialog.showMessageBox({
      type: "info",
      title: t("dialogs.customTitle"),
      message: t("dialogs.customMessage"),
      detail: t("dialogs.additionalInfo"),
      cancelId: 1, // Index of buttons used to cancel dialog boxes
      defaultId: 0, // Set default selected button
      buttons: [t("common.confirm"), t("common.cancel")],
    });
    let data =
      res.response === 0
        ? t("dialogs.confirmButtonClicked")
        : t("dialogs.cancelButtonClicked");

    return data;
  }

  /**
   * Select Directory
   */
  async selectFolder(): Promise<string> {
    const result = await dialog.showOpenDialog({
      properties: ["openDirectory", "createDirectory"],
    });

    if (result.canceled || !result.filePaths) {
      return "";
    }

    return result.filePaths[0];
  }

  async selectFileByParams(args): Promise<any> {
    const filterList =
      args == undefined || args.filterList == undefined
        ? [
            {
              name: t("dialogs.all"),
              extensions: ["*"],
            },
          ]
        : args.filterList;
    const title =
      args == undefined || args.title == undefined
        ? t("dialogs.fileSelection")
        : args.title;
    const result = await dialog.showOpenDialog({
      title: title,
      properties: ["openFile"],
      filters: [...filterList],
    });
    if (result.canceled || isEmpty(result.filePaths)) {
      return {};
    }
    const path = result.filePaths?.[0];
    return { path: path };
  }

  async selectFilesByParams(args): Promise<any[]> {
    const resList: any[] = [];
    const filterList =
      args == undefined || args.filterList == undefined
        ? [
            {
              name: t("dialogs.all"),
              extensions: ["*"],
            },
          ]
        : args.filterList;
    const title =
      args == undefined || args.title == undefined
        ? t("dialogs.fileSelection")
        : args.title;
    const result = await dialog.showOpenDialog({
      title: title,
      properties: ["openFile", "multiSelections"],
      filters: [...filterList],
    });
    if (result.canceled || result.filePaths == undefined) {
      return resList;
    }
    for (let file of result.filePaths) {
      const stats = await fs.promises.stat(file);
      const size = stats.size;
      resList.push({ path: file, size: size });
    }
    return resList;
  }

  /**
   * open directory
   */
  openDirectory(args: { id: any }): boolean {
    const { id } = args;
    if (!id) {
      return false;
    }
    let dir = "";
    if (path.isAbsolute(id)) {
      dir = id;
    } else {
      dir = electronApp.getPath(id);
    }

    shell.openPath(dir);
    return true;
  }

  /**
   * Select Picture
   */
  async selectPic(): Promise<string | null> {
    const result = await dialog.showOpenDialog({
      title: t("dialogs.selectPic"),
      properties: ["openFile"],
      filters: [
        { name: t("dialogs.images"), extensions: ["jpg", "png", "gif"] },
      ],
    });
    if (result.canceled || !result.filePaths) {
      return null;
    }

    try {
      const data = await fs.promises.readFile(result.filePaths[0]);
      const pic = "data:image/jpeg;base64," + data.toString("base64");
      return pic;
    } catch (err) {
      console.error(err);
      return null;
    }
  }

  /**
   * 选择文件或文件夹并返回详细信息
   * @param args 选择配置参数
   * @returns 文件或文件夹的详细信息
   */
  async selectFileOrFolder(
    args: {
      selectType?: "file" | "folder" | "both";
      filters?: { name: string; extensions: string[] }[];
    } = {}
  ): Promise<any> {
    const { selectType = "both", filters = [] } = args;

    const properties: Array<"openFile" | "openDirectory" | "multiSelections"> =
      [];
    if (selectType === "file" || selectType === "both") {
      properties.push("openFile");
      properties.push("multiSelections");
    }
    if (selectType === "folder" || selectType === "both") {
      properties.push("openDirectory");
      properties.push("multiSelections");
    }

    const result = await dialog.showOpenDialog({
      properties,
      filters: selectType === "folder" ? [] : filters,
    });

    if (result.canceled || !result.filePaths) {
      return null;
    }

    const results = await Promise.all(
      result.filePaths.map(async (filePath) => {
        try {
          const stats = await fs.promises.stat(filePath);
          const isDir = stats.isDirectory();

          // 获取文件大小
          let fileSize = 0;
          if (!isDir) {
            try {
              fileSize = stats.size;
            } catch (sizeErr: any) {
              console.warn(`Cannot get size for ${filePath}:`, sizeErr.message);
              fileSize = 0;
            }
          }

          return {
            path: filePath,
            name: path.basename(filePath),
            size: fileSize,
            isDirectory: isDir,
            createdAt: stats.birthtime,
            modifiedAt: stats.mtime,
            accessedAt: stats.atime,
            extension: path.extname(filePath),
            dirname: path.dirname(filePath),
          };
        } catch (err: any) {
          console.error(`Error getting file info for ${filePath}:`, err);
          return null;
        }
      })
    );

    const filteredResults = results.filter(Boolean);
    return filteredResults.length === 1 ? filteredResults[0] : filteredResults;
  }

  /**
   * Open a new window
   */
  createWindow(args: any): any {
    const wcid = windowService.createWindow(args);
    return wcid;
  }

  /**
   * Get Window contents id
   */
  getWCid(args: any): any {
    const wcid = windowService.getWCid(args);
    return wcid;
  }

  /**
   * Realize communication between two windows through the transfer of the main process
   */
  window1ToWindow2(args: any): void {
    windowService.communicate(args);
    return;
  }

  /**
   * Realize communication between two windows through the transfer of the main process
   */
  window2ToWindow1(args: any): void {
    windowService.communicate(args);
    return;
  }

  /**
   * Create system notifications
   */
  sendNotification(
    args: {
      title?: string;
      subtitle?: string;
      body?: string;
      silent?: boolean;
    },
    event: any
  ): boolean {
    const { title, subtitle, body, silent } = args;

    const options: any = {};
    if (title) {
      options.title = title;
    }
    if (subtitle) {
      options.subtitle = subtitle;
    }
    if (body) {
      options.body = body;
    }
    if (silent !== undefined) {
      options.silent = silent;
    }
    windowService.createNotification(options, event);

    return true;
  }

  async openSaveFileDialog(): Promise<string | undefined> {
    const result = await dialog.showSaveDialog({});
    return result.canceled ? undefined : result.filePath;
  }

  async openSaveFileDialogByParams(args): Promise<string | undefined> {
    const filterList =
      args == undefined || args.filterList == undefined
        ? [
            {
              name: t("dialogs.all"),
              extensions: ["*"],
            },
          ]
        : args.filterList;
    const title =
      args == undefined || args.title == undefined
        ? t("dialogs.fileSave")
        : args.title;
    const defaultPath =
      args == undefined || args.defaultPath == undefined
        ? ""
        : args.defaultPath;
    const result = await dialog.showSaveDialog({
      title: title,
      defaultPath: defaultPath,
      filters: filterList,
    });
    return result.canceled ? undefined : result.filePath;
  }

  // 文件保存对话框
  async showSaveDialogSync(args: {
    defaultPath?: string;
    extensions?: string[];
  }): Promise<string | null> {
    const { defaultPath, extensions } = args;
    const filters = extensions
      ? [{ name: t("dialogs.files"), extensions }]
      : [{ name: t("dialogs.textFiles"), extensions: ["txt"] }];

    const options: Electron.SaveDialogOptions = {
      defaultPath: defaultPath,
      filters,
    };
    const result = await dialog.showSaveDialog(options);
    if (result.canceled || !result.filePath) {
      return null;
    }
    return result.filePath;
  }
  // 文件打开对话框
  async showOpenDialogSync(args: {
    defaultPath?: string;
    extensions?: string[];
  }): Promise<string[] | null> {
    const { defaultPath, extensions } = args;
    const filters = extensions
      ? [{ name: t("dialogs.files"), extensions }]
      : [{ name: t("dialogs.textFiles"), extensions: ["txt"] }];

    const options: Electron.OpenDialogOptions = {
      defaultPath: defaultPath,
      filters,
    };
    const result = await dialog.showOpenDialog(options);
    if (result.canceled || !result.filePaths) {
      return null;
    }
    return result.filePaths;
  }

  /**
   * 获取目录内容
   * @param args 目录路径参数
   * @returns 目录中的文件和文件夹列表
   */
  async getDirectoryContents(args: { path: string }): Promise<any> {
    const { path: dirPath } = args;

    try {
      const items = await fs.promises.readdir(dirPath);
      const results = await Promise.all(
        items.map(async (item) => {
          try {
            const itemPath = path.join(dirPath, item);
            const stats = await fs.promises.stat(itemPath);
            const isDir = stats.isDirectory();

            // 文件夹大小为 0，仅计算文件本身大小
            let fileSize = 0;
            if (!isDir) {
              fileSize = stats.size;
            }
            logger.info("fileSize", itemPath, fileSize);

            return {
              path: itemPath,
              name: item,
              isDirectory: isDir,
              size: fileSize,
              modifiedAt: stats.mtime,
              createdAt: stats.birthtime,
              accessedAt: stats.atime,
              extension: isDir ? "" : path.extname(item),
              leaf: !isDir,
            };
          } catch (err: any) {
            // 处理权限错误和其他访问错误
            if (
              err.code === "EPERM" ||
              err.code === "EACCES" ||
              err.code === "ENOENT" ||
              err.code === "EBUSY"
            ) {
              console.warn(
                `Access denied or file busy for ${item}:`,
                err.message
              );
              return null;
            }
            console.error(`Error getting info for ${item}:`, err);
            return null;
          }
        })
      );

      const filteredResults = results
        .filter((item): item is NonNullable<typeof item> => item !== null)
        .sort((a, b) => {
          // 文件夹在前，文件在后，然后按名称排序
          if (a.isDirectory && !b.isDirectory) return -1;
          if (!a.isDirectory && b.isDirectory) return 1;
          return a.name.localeCompare(b.name);
        });

      return filteredResults;
    } catch (error) {
      console.error(`Error reading directory ${dirPath}:`, error);
      throw error;
    }
  }

  /**
   * 获取系统根目录
   * @returns 系统根目录列表
   */
  async getRootDirectories(): Promise<any> {
    try {
      // 根据操作系统返回不同的根目录
      const platform = process.platform;
      let rootDirs: Array<{
        path: string;
        name: string;
        isDirectory: boolean;
        leaf: boolean;
      }> = [];

      if (platform === "win32") {
        // Windows系统
        const drives: Array<{
          path: string;
          name: string;
          isDirectory: boolean;
          leaf: boolean;
        }> = [];
        for (let i = 65; i <= 90; i++) {
          // A-Z
          const drive = String.fromCharCode(i) + ":";
          const drivePath = drive + "\\";
          try {
            // 使用异步方法检查驱动器是否存在
            await fs.promises.access(drivePath);
            // 尝试访问驱动器根目录，如果成功则添加
            try {
              await fs.promises.readdir(drivePath);
              drives.push({
                path: drivePath,
                name: drive,
                isDirectory: true,
                leaf: false,
              });
            } catch (accessErr: any) {
              // 如果无法访问驱动器，跳过它
              if (accessErr.code === "EPERM" || accessErr.code === "EACCES") {
                console.warn(
                  `Access denied to drive ${drive}:`,
                  accessErr.message
                );
                continue;
              }
              // 其他错误也跳过
              console.warn(`Cannot access drive ${drive}:`, accessErr.message);
              continue;
            }
          } catch (err: any) {
            // 忽略不存在的驱动器或其他错误
            console.warn(`Error checking drive ${drive}:`, err.message);
            continue;
          }
        }
        rootDirs = drives;
      } else if (platform === "darwin") {
        // macOS系统
        rootDirs = [
          { path: "/", name: "Root", isDirectory: true, leaf: false },
          { path: "/Users", name: "Users", isDirectory: true, leaf: false },
          {
            path: "/Applications",
            name: "Applications",
            isDirectory: true,
            leaf: false,
          },
        ];
      } else {
        // Linux系统
        rootDirs = [
          { path: "/", name: "Root", isDirectory: true, leaf: false },
          { path: "/home", name: "Home", isDirectory: true, leaf: false },
          { path: "/usr", name: "Usr", isDirectory: true, leaf: false },
        ];
      }

      return rootDirs;
    } catch (error) {
      console.error("Error getting root directories:", error);
      throw error;
    }
  }

  /**
   * 获取文件或文件夹大小（字节）- 同步版本
   * @param args { path: string }
   * @returns { size: number }
   * @deprecated 建议使用 getFileOrFolderSizeAsync 或 getFileOrFolderSizeFast
   */
  getFileOrFolderSize(args: { path: string }): { size: number } {
    const { path } = args;
    try {
      const size = getFileOrFolderSize(path);
      return { size };
    } catch (error) {
      logger.error("getFileOrFolderSize error", error);
      return { size: 0 };
    }
  }

  /**
   * 异步获取文件或文件夹大小（字节）
   * @param args { path: string, mode?: 'fast' | 'full', maxDepth?: number, timeout?: number }
   * @returns Promise<{ size: number, error?: string }>
   */
  async getFileOrFolderSizeAsync(args: {
    path: string;
    mode?: "fast" | "full";
    maxDepth?: number;
    timeout?: number;
  }): Promise<{ size: number; error?: string }> {
    const { path, mode = "fast", maxDepth = 5, timeout = 10000 } = args;

    try {
      let size: number;

      if (mode === "fast") {
        // 快速模式：只统计直接子文件
        size = await getFileOrFolderSizeFast(path);
      } else {
        // 完整模式：递归统计所有文件
        size = await getFileOrFolderSizeAsync(path, {
          maxDepth,
          timeout,
          onProgress: (current, total) => {
            // 可以在这里发送进度事件到前端
            logger.debug(`计算进度: ${current}/${total}`);
          },
        });
      }

      return { size };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("getFileOrFolderSizeAsync error", errorMessage);
      return { size: 0, error: errorMessage };
    }
  }

  /**
   * 复制文件
   */
  async copyFile(args: {
    src: string;
    dest: string;
  }): Promise<{ success: boolean; message?: string }> {
    const { src, dest } = args;
    try {
      await fs.promises.copyFile(src, dest);
      return { success: true };
    } catch (err) {
      return {
        success: false,
        message: err instanceof Error ? err.message : String(err),
      };
    }
  }

  /**
   * 获取系统常用文件夹（桌面、文档、下载等）
   * @returns 常用文件夹列表
   */
  getSystemSpecialFolders(): any {
    try {
      const folders: any[] = [];
      const folderDefs: { key: string; name: string }[] = [
        { key: "desktop", name: t("systemFolders.desktop") },
        { key: "documents", name: t("systemFolders.documents") },
        { key: "downloads", name: t("systemFolders.downloads") },
        { key: "music", name: t("systemFolders.music") },
        { key: "pictures", name: t("systemFolders.pictures") },
        { key: "videos", name: t("systemFolders.videos") },
      ];
      folderDefs.forEach((f) => {
        try {
          // electronApp.getPath 需要严格的 key 类型
          const folderPath = electronApp.getPath(
            f.key as
              | "desktop"
              | "documents"
              | "downloads"
              | "music"
              | "pictures"
              | "videos"
              | "home"
              | "appData"
              | "userData"
              | "sessionData"
              | "temp"
              | "exe"
              | "module"
              | "recent"
              | "logs"
              | "crashDumps"
          );
          folders.push({
            path: folderPath,
            name: f.name,
            key: f.key,
            isDirectory: true,
            leaf: false,
          });
        } catch (e) {
          // 某些平台可能不存在部分目录，忽略
        }
      });
      return folders;
    } catch (error) {
      console.error("Error getting system special folders:", error);
      return [];
    }
  }
}

OsController.toString = () => "[class OsController]";

export default OsController;
