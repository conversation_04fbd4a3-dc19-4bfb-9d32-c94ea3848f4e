/**
 * Vite 缓存配置优化
 * 用于提升开发和构建性能
 */

const path = require('path');
const fs = require('fs');

// 缓存目录配置
const CACHE_DIR = path.resolve(__dirname, 'node_modules/.cache');
const VITE_CACHE_DIR = path.join(CACHE_DIR, 'vite');
const TS_CACHE_DIR = path.join(CACHE_DIR, 'typescript');
const ESLINT_CACHE_DIR = path.join(CACHE_DIR, 'eslint');

// 确保缓存目录存在
function ensureCacheDirectories() {
  const dirs = [CACHE_DIR, VITE_CACHE_DIR, TS_CACHE_DIR, ESLINT_CACHE_DIR];
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// 清理过期缓存
function cleanExpiredCache(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7天
  const now = Date.now();
  
  function cleanDirectory(dir) {
    if (!fs.existsSync(dir)) return;
    
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        cleanDirectory(filePath);
      } else if (now - stats.mtime.getTime() > maxAge) {
        try {
          fs.unlinkSync(filePath);
          console.log(`已清理过期缓存文件: ${filePath}`);
        } catch (error) {
          console.warn(`清理缓存文件失败: ${filePath}`, error.message);
        }
      }
    });
  }
  
  cleanDirectory(CACHE_DIR);
}

// 获取缓存统计信息
function getCacheStats() {
  function getDirSize(dir) {
    if (!fs.existsSync(dir)) return 0;
    
    let size = 0;
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        size += getDirSize(filePath);
      } else {
        size += stats.size;
      }
    });
    
    return size;
  }
  
  const formatSize = (bytes) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };
  
  return {
    total: formatSize(getDirSize(CACHE_DIR)),
    vite: formatSize(getDirSize(VITE_CACHE_DIR)),
    typescript: formatSize(getDirSize(TS_CACHE_DIR)),
    eslint: formatSize(getDirSize(ESLINT_CACHE_DIR))
  };
}

module.exports = {
  CACHE_DIR,
  VITE_CACHE_DIR,
  TS_CACHE_DIR,
  ESLINT_CACHE_DIR,
  ensureCacheDirectories,
  cleanExpiredCache,
  getCacheStats
};
