7-Zip 23.01
-----------

7-Zip is a file archiver for Windows.

7-Zip Copyright (C) 1999-2023 <PERSON>.

The main features of 7-Zip: 

  - High compression ratio in the new 7z format
  - Supported formats:
     - Packing / unpacking: 7z, XZ, BZIP2, GZIP, TAR, ZIP and WIM.
     - Unpacking only: APFS, AR, ARJ, Base64, CAB, CHM, CPIO, CramFS, DMG, EXT, FAT, GPT, HFS,
                       IHEX, ISO, LZH, LZMA, MBR, MSI, NSIS, NTFS, QCOW2, RAR, 
                       RPM, SquashFS, UDF, UEFI, VDI, VHD, VHDX, VMDK, XAR and Z.
  - Fast compression and decompression
  - Self-extracting capability for 7z format
  - Strong AES-256 encryption in 7z and ZIP formats
  - Integration with Windows Shell
  - Powerful File Manager
  - Powerful command line version
  - Localizations for 90 languages


7-Zip is free software distributed under the GNU LGPL (except for unRar code).
Read License.txt for more information about license.


  This distribution package contains the following files:

  7zFM.exe      - 7-Zip File Manager
  7-zip.dll     - Plugin for Windows Shell
  7-zip32.dll   - Plugin for Windows Shell (32-bit plugin for 64-bit system) 
  7zg.exe       - GUI module
  7z.exe        - Command line version
  7z.dll        - 7-Zip engine module
  7z.sfx        - SFX module (Windows version)
  7zCon.sfx     - SFX module (Console version)

  License.txt   - License information
  readme.txt    - This file
  History.txt   - History of 7-Zip
  7-zip.chm     - User's Manual in HTML Help format
  descript.ion  - Description for files

  Lang\en.ttt   - English (base) localization file
  Lang\*.txt    - Localization files


---
End of document
