"use strict";

import { logger } from "ee-core/log";
import { ERROR_CODES, ERROR_MESSAGES, handleErrorResponse, handleCustomResponse } from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { matrixService } from "../../service/matrix/matrix";
import { IECReq } from "../../interface/debug/request";
/**
 * MatrixController
 * <AUTHOR>
 * @class
 */
class MatrixController {
  /**
   *  执行任务
   * @returns {Promise<ApiResponse>}
   */
  async runTask(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] runTask - Start", req);
      // 执行任务
      const result = await matrixService.runTask(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixController] runTask - Error occurred", error);
      return handleErrorResponse(error);
    }
  }

  /**
   *  查询装置列表
   * @returns {Promise<ApiResponse>}
   */
  async getDeviceList(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] getDeviceList - Start", req);
      // 查询装置列表
      const result = await matrixService.getDeviceList(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixController] getDeviceList - Error occurred", error);
      return handleErrorResponse(error);
    }
  }

  /**
   *  新增装置
   * @returns {Promise<ApiResponse>}
   */
  async addDevice(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] addDevice - Start", req);
      // 新增装置
      const result = await matrixService.addDevice(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        "[MatrixController] importDeviceList - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   *  删除装置
   * @returns {Promise<ApiResponse>}
   */
  async deleteDevice(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] deleteDevice - Start", req);
      // 删除装置
      const result = await matrixService.deleteDevice(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixController] deleteDevice - Error occurred", error);
      return handleErrorResponse(error);
    }
  }

  /**
   *  更新装置
   * @returns {Promise<ApiResponse>}
   */
  async updateDevice(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] updateDevice - Start", req);
      // 更新装置
      const result = await matrixService.updateDevice(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixController] updateDevice - Error occurred", error);
      return handleErrorResponse(error);
    }
  }

  /**
   *  导入装置列表
   * @returns {Promise<ApiResponse>}
   */
  async importDeviceList(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] importDeviceList - Start", req);
      // 导入装置列表
      const result = await matrixService.importDeviceList(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        "[MatrixController] importDeviceList - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }
  /**
   *  导出装置列表
   * @returns {Promise<ApiResponse>}
   */
  async exportDeviceList(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] exportDeviceList - Start", req);
      // 导出装置列表
      const data = await matrixService.exportDeviceList(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        data
      );
    } catch (error) {
      logger.error(
        "[MatrixController] exportDeviceList - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   *  查询下载文件列表
   * @returns {Promise<ApiResponse>}
   */
  async getDownloadList(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] getDownloadList - Start", req);
      // 查询下载文件列表
      const result = await matrixService.getDeviceList(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        "[MatrixController] getDownloadList - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   *  新增下载文件
   * @returns {Promise<ApiResponse>}
   */
  async addDownloadFile(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] addDownloadFile - Start", req);
      // 新增下载文件
      const result = true;
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        "[MatrixController] addDownloadFile - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   *  删除下载文件
   * @returns {Promise<ApiResponse>}
   */
  async deleteDownloadFile(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] deleteDownloadFile - Start", req);
      // 导入装置列表
      const result = await matrixService.deleteDownFile(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        "[MatrixController] deleteDownloadFile - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   *  更新下载文件
   * @returns {Promise<ApiResponse>}
   */
  async updateDownloadFile(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] updateDownloadFile - Start", req);
      // 导入装置列表
      const result = await matrixService.updateDevice(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        "[MatrixController] updateDownloadFile - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   *  导入下载文件列表
   * @returns {Promise<ApiResponse>}
   */
  async importDownloadList(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] importDownloadList - Start", req);
      // 导入下载文件列表
      const result = await matrixService.importDownloadList(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        "[MatrixController] importDownloadList - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }
  /**
   *  导出下载文件列表
   * @returns {Promise<ApiResponse>}
   */
  async exportDownloadList(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] exportDownloadList - Start", req);
      // 导出下载文件列表
      const flag = await matrixService.exportDownloadList(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      logger.error(
        "[MatrixController] exportDownloadList - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   *  打包文件列表到指定目录
   * @returns {Promise<ApiResponse>}
   */
  async handlePackage(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] handlePackage - Start", req);
      const result = await matrixService.handlePackage(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixController] handlePackage - Error occurred", error);
      return handleErrorResponse(error);
    }
  }

  /**
   *  删除参数
   * @returns {Promise<ApiResponse>}
   */
  async deleteParam(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] importDeviceList - Start", req);
      // 删除参数
      const result = await matrixService.deleteParam(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixController] deleteParam - Error occurred", error);
      return handleErrorResponse(error);
    }
  }

  /**
   *  更新参数
   * @returns {Promise<ApiResponse>}
   */
  async updateParam(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] importDeviceList - Start", req);
      // 更新参数
      const result = await matrixService.updateParam(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixController] updateParam - Error occurred", error);
      return handleErrorResponse(error);
    }
  }
  /**
   *  导入参数定值
   * @returns {Promise<ApiResponse>}
   */
  async importParamList(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] importParamList - Start", req);
      // 导入参数定值
      const result = await matrixService.importParamList(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        "[MatrixController] importParamList - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }
  /**
   *  导出参数定值
   * @returns {Promise<ApiResponse>}
   */
  async exportParanList(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] exportParanList - Start", req);
      // 导出参数定值
      const flag = await matrixService.exportParanList(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      logger.error(
        "[MatrixController] exportParanList - Error occurred",
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   *  清空数据
   * @returns {Promise<ApiResponse>}
   */
  async clearData(req: IECReq<any>): Promise<ApiResponse> {
    try {
      logger.info("[MatrixController] clearData - Start", req);
      // 清空数据
      const result = await matrixService.clearData(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error("[MatrixController] clearData - Error occurred", error);
      return handleErrorResponse(error);
    }
  }
}

MatrixController.toString = () => "[class MatrixController]";
export default MatrixController;
