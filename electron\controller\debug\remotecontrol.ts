"use strict";

import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleInternalError,
  handleConnectionError,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import { SelectRequestData } from "iec-upadrpc/dist/src/data";
import { remoteControlService } from "../../service/debug/remotecontrol";
import { IECReq } from "../../interface/debug/request";

/**
 * 构造装置菜单树
 * @class wzl
 */
class RemoteControlController {
  constructor() {}

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。详情见：控制器文档
   * id 装置唯一id
   * 遥控选择
   */

  async ykSelectWithValue(
    req: IECReq<SelectRequestData>
  ): Promise<ApiResponse> {
    // 遥控选择方法入口日志
    logger.info(
      `[RemoteControlController] ${t("logs.remoteControlController.ykSelectWithValueEntry")}:`,
      JSON.stringify(req)
    );
    try {
      const res = await remoteControlService.ykSelectWithValue(req);
      // 遥控选择方法返回日志
      logger.info(
        `[RemoteControlController] ${t("logs.remoteControlController.ykSelectWithValueReturn")}:`,
        JSON.stringify(res)
      );
      if (res.data.success && res.data.success) {
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          ERROR_MESSAGES.SUCCESS,
          res
        );
      } else if (!res.data.success) {
        return handleCustomResponse(2, res.data.addCauseDesc);
      } else {
        return handleConnectionError();
      }
    } catch (error) {
      // 遥控选择方法异常日志
      logger.error(
        `[RemoteControlController] ${t("logs.remoteControlController.ykSelectWithValueException")}:`,
        error
      );
      return handleInternalError();
    }
  }

  async ykSelectValueConfirm(
    req: IECReq<SelectRequestData>
  ): Promise<ApiResponse> {
    try {
      const res = await remoteControlService.ykSelectValueConfirm(req);
      if (res.data.success && res.data.success) {
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          ERROR_MESSAGES.SUCCESS,
          res
        );
      } else if (!res.data.success) {
        return handleCustomResponse(2, res.data.addCauseDesc);
      } else {
        return handleConnectionError();
      }
    } catch (error) {
      logger.error(
        `[RemoteControlController] ${t("logs.remoteControlController.ykExecuteException")}:`,
        error
      );
      return handleInternalError();
    }
  }

  async ykSelectValueCancel(
    req: IECReq<SelectRequestData>
  ): Promise<ApiResponse> {
    try {
      const res = await remoteControlService.ykSelectValueCancel(req);
      if (res.data.success && res.data.success) {
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          ERROR_MESSAGES.SUCCESS,
          res
        );
      } else if (!res.data.success) {
        return handleCustomResponse(2, res.data.addCauseDesc);
      } else {
        return handleConnectionError();
      }
    } catch (error) {
      logger.error(
        `[RemoteControlController] ${t("logs.remoteControlController.ykSelectValueCancelException")}:`,
        error
      );
      return handleInternalError();
    }
  }
}

RemoteControlController.toString = () => "[class RemoteControlController]";
module.exports = RemoteControlController;
