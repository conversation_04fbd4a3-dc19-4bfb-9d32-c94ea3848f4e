import {ApiResponse} from "../../data/debug/apiResponse";
import {moreService} from "../../service/common/more";
import {
    ERROR_CODES,
    ERROR_MESSAGES,
    handleCustomResponse, handleErrorResponse, handleInternalError
} from "../../data/debug/errorCodes";
import {logger} from "ee-core/log";
import {ExportConfigParam, ImportConfigParam} from "../../interface/common/more";

/**
 * 更多窗口相关
 * @class
 */
class MoreController {
    /**
     * 导入工程配置
     * @param req
     */
    async importConfig(req: ImportConfigParam): Promise<ApiResponse> {
        try {
            logger.info("[MoreController] importConfig - Start", req);
            const res = await moreService.importConfig(req);
            if (!res) {
                return handleInternalError();
            }
            return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS, true);
        } catch (error) {
            logger.error("[MoreController] importConfig - Error occurred", error);
            return handleErrorResponse(error);
        }
    }

    /**
     * 导出工程配置
     * @param req
     */
    async exportConfig(req: ExportConfigParam): Promise<ApiResponse> {
        try {
            logger.info("[MoreController] exportConfig - Start", req);
            const res = await moreService.exportConfig(req);
            if (!res) {
                return handleInternalError();
            }
            return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS, true);
        } catch (error) {
            logger.error("[MoreController] exportConfig - Error occurred", error);
            return handleErrorResponse(error);
        }
    }
}

MoreController.toString = () => '[class MoreController]';

export default MoreController;