/**
 * Matrix task job related - English
 */
export default {
  // Task logs
  logs: {
    taskJobParams: "TaskJob parameters",
    pauseTaskJob: "Pause TaskJob, job ID",
    resumeTaskJob: "Resume TaskJob, job ID, process ID",
    doTaskStart: "Start executing task",
    allFinished: "All tasks completed"
  },

  // Task steps
  steps: {
    connect: "Connect",
    download: "Download", 
    import: "Import"
  },

  // Task messages
  messages: {
    connectDevice: "Connect device",
    executeFileDownload: "Execute file download",
    downloadingFile: "Downloading file",
    downloadFileFailed: "File download failed",
    fileDownloadCompleted: "File download completed",
    executeParamImport: "Execute parameter import",
    paramValidationFailed: "Parameter format validation failed",
    paramImportFailed: "Parameter import failed",
    paramImportCompleted: "Parameter import completed",
    taskCompleted: "Task completed",
    deviceConnectionFailed: "Device connection failed",
    deviceRebootSuccess: "Device reboot successful"
  },

  // Error messages
  errors: {
    paramItemModifyError: "Parameter item modification error, error items are: ",
    paramConfirmError: "Parameter confirmation error, reason: ",
    paramNotFound: "Error reason: corresponding parameter not found",
    invalidValue: "invalid value",
    description: "description",
    errorReason: ", error reason: "
  }
};
