/**
 * 使用 TypeScript 定义模型类，包含字段：序号、变量名称、变量描述、值、最小值、最大值、步长和单位。
 * <AUTHOR>
 */
export default class ParamItem {
  index: number;
  paramName: string;
  paramDesc: string;
  inf: string;
  value: string;
  minValue: string;
  maxValue: string;
  step: string;
  unit: string;
  fc: string;
  grp: string;
  type: string;
  grpName?: string;

  /**
   * 构造函数，初始化 ParamItem 实例。
   * @param options 包含所有属性的对象
   */
  constructor(options: {
    index: number;
    paramName: string;
    inf: string;
    paramDesc: string;
    value: string;
    grp: string;
    minValue?: string;
    maxValue?: string;
    step?: string;
    unit?: string;
    fc: string;
    type: string;
  }) {
    const { index, paramName, inf, fc, paramDesc, value = "", minValue = "", grp = "", maxValue = "", step = "", unit = "", type = "" } = options;

    this.index = index;
    this.inf = inf;
    this.grp = grp;
    this.paramName = paramName;
    this.paramDesc = paramDesc;
    this.value = value;
    this.minValue = minValue;
    this.maxValue = maxValue;
    this.step = step;
    this.unit = unit;
    this.fc = fc;
    this.type = type;
  }
}

export interface AllParamItem {
  grpname: string;
  data: ParamItem[];
}
