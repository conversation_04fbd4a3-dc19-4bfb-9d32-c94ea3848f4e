/**
 * Relacionado con variables - Español
 */
export default {
  registerFailed: "Registro de variable fallido, razón:",
  modifyFailed: "Modificación de variable fallida, razón:",
  deleteFailed: "Eliminación de variable fallida, razón:",
  modifyError: "Modificación de variable fallida",
  deleteError: "Eliminación de variable fallida",
  debugVariables: "Variables de depuración del dispositivo",
  variableNameExists: "¡El nombre de variable {name} ya existe!",
  variableNameExistsImport: "¡El nombre de variable {name} ya existe!",
  importKeyMapping: {
    variableName: "Nombre de Variable"
  },
  headers: {
    index: "Índice",
    name: "Nombre de Variable",
    description: "Descripción de Variable",
    type: "Tipo de Variable",
    value: "Valor de Variable"
  }
};
