import { backupService } from "../../service/debug/backup";
import { ApiResponse } from "../../data/debug/apiResponse";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleCustomResponse,
  handleErrorResponse,
} from "../../data/debug/errorCodes";
import { IECReq } from "../../interface/debug/request";

export default {
  /** 一键备份接口 */
  async oneKeyBackup(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const { backupRoot, types, taskId } = req.data;
      const { head } = req;
      const result = await backupService.oneKeyBackup({
        backupRoot,
        types,
        head,
        taskId,
      });
      // backupService 已返回 ApiResponse
      return result;
    } catch (error) {
      return handleErrorResponse(error);
    }
  },
  /** 取消备份接口 */
  async cancelBackup(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const result = backupService.cancelBackup(req.head, req.data);
      return result;
    } catch (error) {
      return handleErrorResponse(error);
    }
  },
};
