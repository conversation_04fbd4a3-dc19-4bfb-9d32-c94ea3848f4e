"use strict";
import { deviceConnectService } from "../../service/debug/deviceconnect";
import { deviceOperationService } from "../../service/debug/deviceoperation";

import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleErrorResponse,
  handleConnectionError,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { IECReq } from "../../interface/debug/request";

/**
 * 装置操作相关
 * <AUTHOR>
 * @class
 */
class DeviceOperationController {
  constructor() {
    logger.info(
      `[DeviceOperationController] ${t("logs.deviceOperationController.initialized")}`
    );
  }

  /**
   * 手动录波
   * manualWave
   */
  async manualWave(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceOperationController] ${t("logs.deviceOperationController.manualWaveStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceOperationController] ${t("logs.deviceOperationController.manualWaveCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceOperationController] ${t("logs.deviceOperationController.manualWaveNotConnected")}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceOperationController] ${t("logs.deviceOperationController.manualWaveConnected")}`
      );
      const result = await deviceOperationService.manualWave(req);

      logger.info(
        `[DeviceOperationController] ${t("logs.deviceOperationController.manualWaveSuccess")}:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceOperationController] ${t("logs.deviceOperationController.manualWaveException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 清除报告
   * clearWave
   */
  async clearWave(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceOperationController] ${t("logs.deviceOperationController.clearWaveStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceOperationController] ${t("logs.deviceOperationController.clearWaveCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceOperationController] ${t("logs.deviceOperationController.clearWaveNotConnected")}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceOperationController] ${t("logs.deviceOperationController.clearWaveConnected")}`
      );
      const result = await deviceOperationService.clearWave(req);

      logger.info(
        `[DeviceOperationController] ${t("logs.deviceOperationController.clearWaveSuccess")}:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceOperationController] ${t("logs.deviceOperationController.clearWaveException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 装置复归
   * resetDevice
   */
  async resetDevice(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceOperationController] ${t("logs.deviceOperationController.resetDeviceStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceOperationController] ${t("logs.deviceOperationController.resetDeviceCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceOperationController] ${t("logs.deviceOperationController.resetDeviceNotConnected")}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceOperationController] ${t("logs.deviceOperationController.resetDeviceConnected")}`
      );
      const result = await deviceOperationService.resetDevice(req);

      logger.info(
        `[DeviceOperationController] ${t("logs.deviceOperationController.resetDeviceSuccess")}:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceOperationController] ${t("logs.deviceOperationController.resetDeviceException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 清除报告
   * clearReport
   */
  async clearReport(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceOperationController] ${t("logs.deviceOperationController.clearReportStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceOperationController] ${t("logs.deviceOperationController.clearReportCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceOperationController] ${t("logs.deviceOperationController.clearReportNotConnected")}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceOperationController] ${t("logs.deviceOperationController.clearReportConnected")}`
      );
      const result = await deviceOperationService.clearReport(req);

      logger.info(
        `[DeviceOperationController] ${t("logs.deviceOperationController.clearReportSuccess")}:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceOperationController] ${t("logs.deviceOperationController.clearReportException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 装置重启
   * rebootDevice
   */
  async rebootDevice(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceOperationController] ${t("logs.deviceOperationController.rebootDeviceStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceOperationController] ${t("logs.deviceOperationController.rebootDeviceCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceOperationController] ${t("logs.deviceOperationController.rebootDeviceNotConnected")}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceOperationController] ${t("logs.deviceOperationController.rebootDeviceConnected")}`
      );
      const result = await deviceOperationService.rebootDevice(req);

      logger.info(
        `[DeviceOperationController] ${t("logs.deviceOperationController.rebootDeviceSuccess")}:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceOperationController] ${t("logs.deviceOperationController.rebootDeviceException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }
}

DeviceOperationController.toString = () => "[class DeviceOperationController]";
export default DeviceOperationController;
