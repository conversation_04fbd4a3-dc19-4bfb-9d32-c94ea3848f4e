/**
 * Services related - English
 */
export default {
  // License service
  license: {
    cacheResult: "Using cached result",
    verificationTime: "Verification time",
    verificationFailed: "Verification failed, time",
    businessErrorInfo: "Get business error information",
    getMachineCode: "Get machine code",
    checkAuth: "Check authorization",
    activate: "Activate license",
  },

  // Cross-platform service
  cross: {
    startGoService: "Starting Go service...",
    goServiceStartSuccess: "Go service started successfully, time",
    goServiceStartFailed: "Go service start failed, time",
    startPythonService: "Starting Python service...",
    pythonServiceStartSuccess: "Python service started successfully, time",
    pythonServiceStartFailed: "Python service start failed, time",
    optimizeStartParams: "Optimize startup parameters",
  },

  // Base service
  base: {
    getClientStart: "Start getting device client, device ID",
    deviceNotFound: "Device information not found, device ID",
    deviceNotConnected: "Device not connected or client invalid, device ID",
    getClientSuccess: "Successfully got device client, device ID",
  },

  // Device connection service
  deviceConnect: {
    deviceConnected: "Device connected",
    connectionAttempt: "Connection attempt",
    clientObtained: "Client obtained, device ID",
    clientNotObtained: "Client not obtained, device ID",
    connectionException: "Connection exception",
    connectionCheckException: "Connection check exception",
    connectDeviceByRpc: "Connect device by RPC",
    callInterface: "Call interface",
    useDeviceIdAsKey: "Use device ID as key",
    getConnectionFromGlobal: "Get connection object from global variables",
    notifyFrontendConnectionStatus:
      "Notify frontend to modify connection status",
    retryInterval: "Optional: interval between retries",
    cacheSuccessfulConnection: "Cache successful connection object",
    resetData: "Reset data",
    compareMd5AndSetFilePath: "Compare MD5 and set file path",
    addDebugInfoToGlobal:
      "Add debugInfo and debugItemMap to realSingleGlobalDeviceInfo",
    connectionSuccessful: "Connection successful, device ID",
    disconnectDevice: "Disconnect device",
    getConnection: "Get connection",
    deleteConnectionObject: "Delete connection object",
    disconnectSuccessful: "Disconnect successful, device ID",
    disconnectException: "Disconnect exception",
  },

  // Device information service
  deviceInfo: {
    getDeviceInfoStart: "Start getting device information",
    exportStart: "Start exporting device information",
    exportSuccess: "Export device information successful",
  },

  // Configuration service
  configure: {
    getConfigureList: "Get configuration list",
    addConfigure: "Add configuration",
    setId: "Set ID",
    projectNotExists: "Project does not exist",
    duplicateName: "Duplicate name, please re-enter",
    addConfigureException: "Add configuration exception",
    projectNotFound: "Project not found",
    projectPathNotFound: "Project path not found",
    replaceWithNew: "Replace with new content",
    projectNotFoundShort: "Project not found",
    operationTypeIncorrect:
      "Operation type incorrect, allowed values are [project,hmi]",
    renameConfigureException: "Rename configuration exception",
    getConfigureListException: "Get configuration list exception",
    configureSaveException: "Configuration save exception",
    openConfigureFolder: "Open configuration folder",
  },

  // Window service
  window: {
    windowStateInitialized: "Window state management initialized",
    windowStateInitFailed: "Window state management initialization failed",
    windowStateSaved: "Window state saved",
    saveWindowStateFailed: "Save window state failed",
    windowStateRestored: "Window state restored",
    restoreWindowStateFailed: "Restore window state failed",
    clickNotification: "Click notification",
    closeNotification: "Close notification",
    createWindow: "Create window",
    windowCreated: "Window created",
    windowCreationFailed: "Window creation failed",
    getWindowId: "Get window ID",
    windowCommunication: "Window communication",
    notificationCreated: "Notification created",
    windowClosed: "Window closed",
    windowMaximized: "Window maximized",
    windowMinimized: "Window minimized",
    windowDragged: "Window drag completed",
    screenshotTaken: "Screenshot saved",
    devToolsOpened: "Developer tools opened",
    devToolsClosed: "Developer tools closed",
  },

  // System events service
  systemEvents: {
    systemEventsInitialized: "System events monitoring initialized",
    eventListenersSet: "System event listeners set",
    setupEventListenersFailed: "Setup event listeners failed",
    systemSuspending: "System suspending",
    systemResuming: "System resuming",
    screenLocked: "Screen locked",
    screenUnlocked: "Screen unlocked",
    systemShuttingDown: "System shutting down",
    appBeforeQuit: "Application before quit",
    allWindowsClosed: "All windows closed",
    appActivated: "Application activated",
    windowBlurred: "Window blurred",
    windowFocused: "Window focused",
    windowMinimized: "Window minimized",
    windowRestored: "Window restored",
    applicationStateSaved: "Application state saved",
    applicationStateRestored: "Application state restored",
    powerInfoRetrieved: "Power information retrieved",
    cleanupCompleted: "Cleanup completed",
  },

  // Tray service
  tray: {
    trayLoaded: "Tray loaded",
    trayCreated: "Tray created",
    trayCreationFailed: "Tray creation failed",
  },

  // Database service
  database: {
    databaseConnected: "Database connected",
    databaseConnectionFailed: "Database connection failed",
    queryExecuted: "Query executed",
    queryFailed: "Query failed",
    dataInserted: "Data inserted",
    dataUpdated: "Data updated",
    dataDeleted: "Data deleted",
    transactionStarted: "Transaction started",
    transactionCommitted: "Transaction committed",
    transactionRolledBack: "Transaction rolled back",
    tableCreated: "Table created",
    tableCreationFailed: "Table creation failed",
    dataInsertFailed: "Data insert failed",
    dataDeleteFailed: "Data delete failed",
    dataUpdateFailed: "Data update failed",
    dataDirRetrieved: "Data directory retrieved",
    dataDirRetrieveFailed: "Data directory retrieve failed",
    customDataDirSet: "Custom data directory set",
    customDataDirSetFailed: "Custom data directory set failed",
  },

  // HMI service
  hmi: {
    graphDefined: "Graph defined",
    configurationLoaded: "Configuration loaded",
    viewCreated: "View created",
    projectOpened: "Project opened",
    projectSaved: "Project saved",
    elementAdded: "Element added",
    elementModified: "Element modified",
    elementDeleted: "Element deleted",
  },

  // Job service
  job: {
    jobStarted: "Job started",
    jobCompleted: "Job completed",
    jobFailed: "Job failed",
    jobCancelled: "Job cancelled",
    jobPaused: "Job paused",
    jobResumed: "Job resumed",
    jobScheduled: "Job scheduled",
    jobRemoved: "Job removed",
  },

  // Matrix service
  matrix: {
    matrixInitialized: "Matrix initialized",
    matrixCalculationStarted: "Matrix calculation started",
    matrixCalculationCompleted: "Matrix calculation completed",
    matrixOperationFailed: "Matrix operation failed",
    dataProcessed: "Data processed",
    algorithmExecuted: "Algorithm executed",
    encryptCompressionComplete: "Encrypt compression complete",

    // Export related
    exportDeviceListEntry: "Export device list entry",
    exportDeviceListStart: "Start exporting device list",
    exportDeviceListComplete: "Device list export completed",
    deviceListSheetName: "Device List",
    exportDownloadListStart: "Start exporting download list",
    exportParamListStart: "Start exporting parameter list",

    // Header translations
    indexHeader: "Index",
    deviceNameHeader: "Device Name",
    deviceAddressHeader: "Device Address",
    devicePortHeader: "Device Port",
    encryptedHeader: "Encrypted",
    downFileHeader: "Download File",
    importParamHeader: "Import Parameter",
    connectTimeoutHeader: "Connect Timeout",
    paramTimeoutHeader: "Parameter Timeout",
    readTimeoutHeader: "Read Timeout",
    fileNameHeader: "File Name",
    fileSizeHeader: "File Size",
    filePathHeader: "File Path",
    lastModifiedHeader: "Last Modified",
    paramNameHeader: "Name",
    paramDescHeader: "Description",
    valueHeader: "Value",
    minValueHeader: "Min Value",
    maxValueHeader: "Max Value",
    stepHeader: "Step",
    unitHeader: "Unit",
  },

  // More service
  more: {
    importPathNotExists: "Import path does not exist",
    exportPathNotExists: "Export path does not exist",
    selectCorrectConfigFile: "Please select the correct configuration file",
    exportProjectConfigException: "Export project configuration exception",
    importProjectConfigException: "Import project configuration exception",
    deviceConfigNotExists: "Device configuration file does not exist",
    configurePathNotExists: "Configuration path does not exist",
    unsupportedConfigType: "Unsupported configuration type",
    deviceConfigNotFoundInZip: "Device configuration file not found in zip",
    configureNotFoundInZip: "Configuration not found in zip",
  },
};
