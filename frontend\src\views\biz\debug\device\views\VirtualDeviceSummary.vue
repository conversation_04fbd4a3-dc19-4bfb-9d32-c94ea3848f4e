<template>
  <div class="virtual-device-summary">
    <!-- 功能特性区域 -->
    <div class="features-section">
      <div class="section-header">
        <h2 class="section-title">{{ t("device.groupInfo.virtualTitle") }}</h2>
        <p class="section-subtitle">{{ t("device.virtualSummary.description") }}</p>
      </div>

      <div class="feature-grid">
        <div class="feature-card" v-for="(feature, index) in features" :key="feature.fc" :class="`feature-card-${index % 3}`">
          <div class="feature-header">
            <div class="feature-icon">
              <svg-icon :icon="feature.icon" class="icon" />
            </div>
            <div class="feature-badge">{{ index + 1 }}</div>
          </div>
          <div class="feature-body">
            <h4 class="feature-title">{{ feature.title }}</h4>
            <p class="feature-desc">{{ feature.description }}</p>
          </div>
          <div class="feature-overlay"></div>
        </div>
      </div>
    </div>

    <!-- 信息卡片区域 -->
    <!-- 移除优势特点和使用说明卡片，保持界面简洁 -->
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const features = computed(() => [
  {
    fc: "read_analoy_para",
    title: t("device.virtualSummary.analogParam.title"),
    description: t("device.virtualSummary.analogParam.description"),
    icon: "material-symbols:analytics-outline"
  },
  {
    fc: "read_bi_para",
    title: t("device.virtualSummary.digitalInput.title"),
    description: t("device.virtualSummary.digitalInput.description"),
    icon: "material-symbols:input"
  },
  {
    fc: "read_bo_para",
    title: t("device.virtualSummary.digitalOutput.title"),
    description: t("device.virtualSummary.digitalOutput.description"),
    icon: "material-symbols:output"
  },
  {
    fc: "read_fault_para",
    title: t("device.virtualSummary.faultParam.title"),
    description: t("device.virtualSummary.faultParam.description"),
    icon: "material-symbols:error-outline"
  },
  {
    fc: "read_led_para",
    title: t("device.virtualSummary.ledParam.title"),
    description: t("device.virtualSummary.ledParam.description"),
    icon: "material-symbols:lightbulb-outline"
  },
  {
    fc: "wave_replay",
    title: t("device.virtualSummary.waveReplay.title"),
    description: t("device.virtualSummary.waveReplay.description"),
    icon: "material-symbols:replay"
  }
]);
</script>

<style scoped lang="scss">
.virtual-device-summary {
  height: 100%;
  max-height: 100vh;
  overflow-y: auto;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  // 主标题区域
  .hero-section {
    position: relative;
    padding: 40px 20px;
    background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
    overflow: hidden;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;

    .hero-content {
      text-align: center;
      position: relative;
      z-index: 2;
      max-width: 600px;

      .hero-icon {
        margin-bottom: 16px;
        display: inline-block;
        padding: 20px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);

        .main-icon {
          font-size: 48px;
          color: white;
          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }
      }

      .hero-title {
        font-size: 32px;
        font-weight: 700;
        color: white;
        margin: 0 0 12px 0;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        letter-spacing: -0.5px;
      }

      .hero-subtitle {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
        line-height: 1.6;
        font-weight: 400;
      }
    }

    .hero-decoration {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;

      .decoration-circle {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.08);
        animation: float 6s ease-in-out infinite;

        &.circle-1 {
          width: 300px;
          height: 300px;
          top: -150px;
          right: -150px;
          animation-delay: 0s;
        }

        &.circle-2 {
          width: 200px;
          height: 200px;
          bottom: -100px;
          left: -100px;
          animation-delay: 2s;
        }

        &.circle-3 {
          width: 120px;
          height: 120px;
          top: 20%;
          right: 10%;
          animation-delay: 4s;
        }
      }
    }

    // 添加浮动动画
    @keyframes float {
      0%,
      100% {
        transform: translateY(0px) scale(1);
        opacity: 0.08;
      }
      50% {
        transform: translateY(-20px) scale(1.1);
        opacity: 0.12;
      }
    }
  }

  // 功能特性区域
  .features-section {
    padding: 40px 20px;
    background: transparent;

    .section-header {
      text-align: center;
      margin-bottom: 35px;

      .section-title {
        font-size: 28px;
        font-weight: 700;
        color: var(--el-text-color-primary);
        margin: 0 0 14px 0;
        position: relative;
        letter-spacing: -0.5px;

        &::after {
          content: "";
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
          width: 70px;
          height: 3px;
          background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-3));
          border-radius: 2px;
        }
      }

      .section-subtitle {
        font-size: 17px;
        color: var(--el-text-color-regular);
        margin: 0;
        font-weight: 400;
      }
    }

    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      max-width: 1100px;
      margin: 0 auto;
    }

    .feature-card {
      position: relative;
      background: white;
      border-radius: 14px;
      padding: 24px 20px;
      box-shadow:
        0 3px 16px rgba(0, 0, 0, 0.06),
        0 1px 6px rgba(0, 0, 0, 0.04);
      transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
      overflow: hidden;
      border: 1px solid rgba(0, 0, 0, 0.05);
      text-align: center;
      backdrop-filter: blur(10px);

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, transparent, var(--el-color-primary), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
          0 12px 40px rgba(0, 0, 0, 0.12),
          0 6px 16px rgba(0, 0, 0, 0.08);

        &::before {
          opacity: 1;
        }

        .feature-overlay {
          opacity: 0.02;
        }

        .feature-icon {
          transform: scale(1.05);

          .icon {
            transform: scale(1.1) rotate(5deg);
          }
        }

        .feature-badge {
          transform: scale(1.1);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
        }
      }

      // 根据索引添加不同的主题色
      &.feature-card-0 {
        .feature-icon {
          background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
          .icon {
            color: #1976d2;
          }
        }
        .feature-badge {
          background: #1976d2;
        }
      }

      &.feature-card-1 {
        .feature-icon {
          background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
          .icon {
            color: #388e3c;
          }
        }
        .feature-badge {
          background: #388e3c;
        }
      }

      &.feature-card-2 {
        .feature-icon {
          background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
          .icon {
            color: #f57c00;
          }
        }
        .feature-badge {
          background: #f57c00;
        }
      }

      .feature-header {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        position: relative;

        .feature-icon {
          width: 68px;
          height: 68px;
          border-radius: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
          box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);

          .icon {
            font-size: 32px;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
          }
        }

        .feature-badge {
          position: absolute;
          top: -10px;
          right: -10px;
          width: 28px;
          height: 28px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 13px;
          font-weight: 700;
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          border: 2px solid white;
        }
      }

      .feature-body {
        .feature-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0 0 10px 0;
          line-height: 1.4;
          letter-spacing: -0.2px;
        }

        .feature-desc {
          font-size: 14px;
          color: var(--el-text-color-regular);
          margin: 0;
          line-height: 1.5;
          opacity: 0.8;
        }
      }

      .feature-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--el-color-primary);
        opacity: 0;
        transition: opacity 0.4s ease;
        pointer-events: none;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .virtual-device-summary {
    .features-section {
      .feature-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 18px;
      }
    }
  }
}

@media (max-width: 768px) {
  .virtual-device-summary {
    .hero-section {
      padding: 28px 18px;
      min-height: 140px;

      .hero-content {
        .hero-icon {
          padding: 14px;
          margin-bottom: 10px;

          .main-icon {
            font-size: 30px;
          }
        }

        .hero-title {
          font-size: 22px;
        }

        .hero-subtitle {
          font-size: 14px;
        }
      }
    }

    .features-section {
      padding: 28px 16px;

      .section-header {
        margin-bottom: 24px;

        .section-title {
          font-size: 22px;
        }

        .section-subtitle {
          font-size: 15px;
        }
      }

      .feature-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        max-width: 100%;
      }

      .feature-card {
        padding: 20px;

        .feature-header {
          margin-bottom: 16px;

          .feature-icon {
            width: 56px;
            height: 56px;

            .icon {
              font-size: 26px;
            }
          }

          .feature-badge {
            width: 24px;
            height: 24px;
            font-size: 11px;
            top: -8px;
            right: -8px;
          }
        }

        .feature-body {
          .feature-title {
            font-size: 16px;
          }

          .feature-desc {
            font-size: 13px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .virtual-device-summary {
    .hero-section {
      padding: 16px 12px;

      .hero-content {
        .hero-icon {
          padding: 8px;

          .main-icon {
            font-size: 24px;
          }
        }

        .hero-title {
          font-size: 18px;
        }

        .hero-subtitle {
          font-size: 12px;
        }
      }
    }

    .features-section {
      padding: 16px 8px;

      .section-header {
        .section-title {
          font-size: 18px;
        }

        .section-subtitle {
          font-size: 13px;
        }
      }

      .feature-card {
        padding: 14px 12px;

        .feature-header {
          .feature-icon {
            width: 44px;
            height: 44px;

            .icon {
              font-size: 20px;
            }
          }

          .feature-badge {
            width: 20px;
            height: 20px;
            font-size: 9px;
          }
        }

        .feature-body {
          .feature-title {
            font-size: 14px;
          }

          .feature-desc {
            font-size: 11px;
          }
        }
      }
    }
  }
}
</style>
