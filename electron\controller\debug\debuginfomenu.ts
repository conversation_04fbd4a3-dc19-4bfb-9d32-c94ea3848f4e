"use strict";

import { debugInfoMenuService } from "../../service/debug/debuginfomenu";
import { deviceConnectService } from "../../service/debug/deviceconnect";

import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleInternalError,
  handleConnectionError,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { MenuIdName } from "../../interface/debug/debuginfo";
import { IECReq } from "../../interface/debug/request";

/**
 * 构造装置菜单树
 * @class wzl
 */
class DebugInfoMenuController {
  constructor() {
    logger.info(
      `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.initialized")}`
    );
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。详情见：控制器文档
   * id 装置唯一id
   */

  async getDeviceMenuTree(id: string): Promise<ApiResponse> {
    logger.info(
      `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getDeviceMenuTreeStart")}: ${id}`
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getDeviceMenuTreeCheckConnection")}: ${id}`
      );
      const globalDeviceData = GlobalDeviceData.getInstance();
      const client = globalDeviceData.getClient(id);

      if (!client?.isConnected) {
        logger.warn(
          `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getDeviceMenuTreeNotConnected")}: ${id}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getDeviceMenuTreeConnected")}`
      );
      const deviceMenuTree = await debugInfoMenuService.getTreeMenu(id);

      logger.info(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getDeviceMenuTreeSuccess")}: ${deviceMenuTree?.length || 0}`
      );

      if (deviceMenuTree && deviceMenuTree.length > 0) {
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          ERROR_MESSAGES.SUCCESS,
          deviceMenuTree
        );
      } else {
        logger.warn(
          `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getDeviceMenuTreeEmpty")}: ${id}`
        );
        return handleInternalError();
      }
    } catch (error) {
      logger.error(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getDeviceMenuTreeException")}: ${id}`,
        error
      );
      return handleInternalError();
    }
  }

  // 从缓存中获取对应菜单的数据
  public async getTreeItemByName(
    req: IECReq<MenuIdName>
  ): Promise<ApiResponse> {
    logger.info(
      `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getTreeItemByNameStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getTreeItemByNameCheckConnection")}: ${req.head.id}`
      );
      const connected = await deviceConnectService.isConnected(req.head.id);

      if (!connected) {
        logger.warn(
          `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getTreeItemByNameNotConnected")}: ${req.head.id}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getTreeItemByNameConnected")}`
      );
      const data = debugInfoMenuService.getTreeItemByName(req.data);

      logger.info(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getTreeItemByNameSuccess")}:`,
        data.length
      );

      if (data) {
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          ERROR_MESSAGES.SUCCESS,
          data
        );
      }

      logger.warn(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getTreeItemByNameNotFound")}: ${req.head.id}`
      );
      return handleInternalError();
    } catch (error) {
      logger.error(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getTreeItemByNameException")}: ${req.head.id}`,
        error
      );
      return handleInternalError();
    }
  }

  public async getGroupInfoList(req: IECReq<MenuIdName>): Promise<ApiResponse> {
    logger.info(
      `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getGroupInfoListStart")}:`,
      req
    );

    try {
      logger.debug(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getGroupInfoListCheckConnection")}: ${req.head.id}`
      );
      const connected = await deviceConnectService.isConnected(req.head.id);

      if (!connected) {
        logger.warn(
          `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getGroupInfoListNotConnected")}: ${req.head.id}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getGroupInfoListConnected")}: ${req.head.id}`
      );
      const data = debugInfoMenuService.getGroupInfoList(req);

      logger.info(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getGroupInfoListSuccess")}:`,
        data
      );

      if (data) {
        return handleCustomResponse(
          ERROR_CODES.SUCCESS,
          ERROR_MESSAGES.SUCCESS,
          data
        );
      }

      logger.warn(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getGroupInfoListNotFound")}: ${req.head.id}`
      );
      return handleInternalError();
    } catch (error) {
      logger.error(
        `[DebugInfoMenuController] ${t("logs.debugInfoMenuController.getGroupInfoListException")}: ${req.head.id}`,
        error
      );
      return handleInternalError();
    }
  }
}

DebugInfoMenuController.toString = () => "[class DebugInfoMenuController]";
module.exports = DebugInfoMenuController;
