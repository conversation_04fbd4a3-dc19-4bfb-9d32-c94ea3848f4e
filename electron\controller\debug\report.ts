"use strict";
import { reportService } from "../../service/debug/report";
import { logger } from "ee-core/log";
import { IECReq } from "../../interface/debug/request";
import {
  AuditLogRequestData,
  EventReportRequestData,
  FaultReportRequestData,
  OperateReportRequestData,
} from "iec-upadrpc/dist/src/data";
import {
  IECReportExportParam,
  IECRpcReportOpenWaveParam,
} from "../../interface/debug/report";
import { ERROR_CODES, ERROR_MESSAGES } from "../../data/debug/errorCodes";
import { IECResult } from "iec-common/dist/data/iecdata";
import { t } from "../../data/i18n/i18n";

/**
 * 报告相关Controller
 * <AUTHOR>
 * @class
 */
class ReportController {
  // 获取通用报告，成功返回报告列表，失败返回空列表
  async getCommonReportList(req: IECReq<EventReportRequestData>): Promise<any> {
    // 获取通用报告方法入口日志
    logger.info(
      `[ReportController] ${t("logs.reportController.getCommonReportListEntry")}:`,
      JSON.stringify(req)
    );
    try {
      const reportList = await reportService.getCommonReportList(req);
      // 获取通用报告方法返回日志
      logger.info(
        `[ReportController] ${t("logs.reportController.getCommonReportListReturn")}:`,
        JSON.stringify(reportList)
      );
      return reportList;
    } catch (error) {
      // 获取通用报告方法异常日志
      logger.error(
        `[ReportController] ${t("logs.reportController.getCommonReportListError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 获取整组报告，成功返回报告列表，失败返回空列表
  async getGroupReportList(req: IECReq<FaultReportRequestData>): Promise<any> {
    logger.info(
      `[ReportController] ${t("logs.reportController.getGroupReportStart")}:`,
      req
    );
    try {
      const reportList = await reportService.getGroupReportList(req);
      return reportList;
    } catch (error) {
      logger.error(
        `[ReportController] ${t("logs.reportController.getGroupReportError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 获取操作报告，成功返回报告列表，失败返回空列表
  async getOperateReportList(
    req: IECReq<OperateReportRequestData>
  ): Promise<any> {
    logger.info(
      `[ReportController] ${t("logs.reportController.getOperateReportStart")}:`,
      req
    );
    try {
      const reportList = await reportService.getOperateReportList(req);
      return reportList;
    } catch (error) {
      logger.error(
        `[ReportController] ${t("logs.reportController.getOperateReportError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 获取审计报告，成功返回报告列表，失败返回空列表
  async getAuditLogList(req: IECReq<AuditLogRequestData>): Promise<any> {
    logger.info(
      `[ReportController] ${t("logs.reportController.getAuditReportStart")}:`,
      req
    );
    try {
      const reportList = await reportService.getAuditLogReportList(req);
      return reportList;
    } catch (error) {
      logger.error(
        `[ReportController] ${t("logs.reportController.getAuditReportError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 导出通用报告
  async exportCommonReport(req: IECReq<IECReportExportParam>): Promise<any> {
    logger.info(
      `[ReportController] ${t("logs.reportController.exportCommonReportStart")}:`,
      req.data.type
    );
    try {
      const res = await reportService.exportCommonReport(req);
      return res;
    } catch (error) {
      logger.error(
        `[ReportController] ${t("logs.reportController.exportCommonReportError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 清除报告
  async clearReport(req: IECReq<undefined>): Promise<any> {
    logger.info(
      `[ReportController] ${t("logs.reportController.clearReportStart")}`
    );
    try {
      const res = await reportService.clearReport(req);
      return res;
    } catch (error) {
      logger.error(
        `[ReportController] ${t("logs.reportController.clearReportError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 刷新报告，成功返回报告列表，失败返回空列表
  async refreshReport(req: any): Promise<any> {
    logger.info(
      `[ReportController] ${t("logs.reportController.refreshReportStart")}:`,
      req
    );
    try {
      const reportList = await reportService.refreshReport(req);
      return reportList;
    } catch (error) {
      logger.error(
        `[ReportController] ${t("logs.reportController.refreshReportError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 刷新整组报告，成功返回报告列表，失败返回空列表
  async refreshGroupReport(req: any): Promise<any> {
    logger.info(
      `[ReportController] ${t("logs.reportController.refreshGroupReportStart")}:`,
      req
    );
    try {
      const reportList = await reportService.refreshGroupReport(req);
      return reportList;
    } catch (error) {
      logger.error(
        `[ReportController] ${t("logs.reportController.refreshGroupReportError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 刷新操作报告，成功返回报告列表，失败返回空列表
  async refreshOperateReport(req: any): Promise<any> {
    logger.info(
      `[ReportController] ${t("logs.reportController.refreshOperateReportStart")}:`,
      req
    );
    try {
      const reportList = await reportService.refreshOperateReport(req);
      return reportList;
    } catch (error) {
      logger.error(
        `[ReportController] ${t("logs.reportController.refreshOperateReportError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 刷新动作报告，成功返回报告列表，失败返回空列表
  async refreshTripReport(req: any): Promise<any> {
    logger.info(
      `[ReportController] ${t("logs.reportController.refreshTripReportStart")}:`,
      req
    );
    try {
      const reportList = await reportService.refreshTripReport(req);
      return reportList;
    } catch (error) {
      logger.error(
        `[ReportController] ${t("logs.reportController.refreshTripReportError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 录波文件上招
  async uploadWave(req: IECReq<any>): Promise<any> {
    logger.info(
      `[ReportController] ${t("logs.reportController.uploadWaveStart")}:`,
      req
    );
    try {
      const res = await reportService.uploadWave(req);
      return res;
    } catch (error) {
      logger.error(
        `[ReportController] ${t("logs.reportController.uploadWaveError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 取消录波文件上招
  async cancelUpload(req: any): Promise<any> {
    logger.info(
      `[report] ${t("logs.reportController.cancelUploadStart")}:`,
      req
    );
    try {
      const res = await reportService.cancelUpload(req);
      return res;
    } catch (error) {
      logger.error(
        `[report] ${t("logs.reportController.cancelUploadError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  // 打开录波文件
  async openWaveFile(req: IECReq<IECRpcReportOpenWaveParam>): Promise<any> {
    logger.info(
      `[report] ${t("logs.reportController.openWaveFileStart")}:`,
      req
    );
    try {
      const res = await reportService.openWaveFile(req);
      return res;
    } catch (error) {
      logger.error(
        `[report] ${t("logs.reportController.openWaveFileError")}:`,
        error
      );
      return this.genErrorRes();
    }
  }

  genErrorRes() {
    const errRes = new IECResult();
    errRes.code = ERROR_CODES.OPERATE_FAILED;
    errRes.msg = ERROR_MESSAGES.OPERATE_FAILED;
    errRes.data = [];
    return errRes;
  }
}

ReportController.toString = () => "[class ReportController]";
export default ReportController;
