"use strict";

import { licenseService } from "../../service/activate/license";
import { logger } from "ee-core/log";
import { ERROR_CODES, ERROR_MESSAGES, handleErrorResponse, handleCustomResponse } from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";

/**
 * License授权
 * <AUTHOR>
 * @class
 */
class LicenseController {
  /**
   * 获取机器码
   * getDeviceFile
   */
  async getMachineCode(): Promise<ApiResponse> {
    try {
      logger.info("[LicenseController] getMacheCode - Start");
      const machineCode = await licenseService.getMachineCode();
      logger.info(
        `[LicenseController] getMacheCode - Success, 机器码: ${machineCode}`
      );
      return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS, machineCode);
    } catch (error) {
      logger.error("[LicenseController] getMacheCode - Error occurred", error);
      return handleErrorResponse(error);
    }
  }

  /**
   * 检查授权
   * checkAuth
   */
  async checkAuth(): Promise<ApiResponse> {
    try {
      logger.info("[LicenseController] checkAuth - Start");
      const authResult = await licenseService.checkAuth();
      logger.info(`[LicenseController] checkAuth - Success, 授权状态: ${authResult ? '已授权' : '未授权'}`);
      return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS, authResult);
    } catch (error) {
      logger.error("[LicenseController] checkAuth - Error occurred", error);
      return handleErrorResponse(error);
    }
  }

  /**
   * 授权激活
   * checkAuth
   */
  async activate(activeCode: string): Promise<ApiResponse> {
    try {
      logger.info(
        `[LicenseController] activate - Start, 激活码: ${activeCode}`
      );
      const activateResult = await licenseService.activate(activeCode);
      logger.info(
        `[LicenseController] activate - Success, 激活结果: ${activateResult ? "激活成功" : "激活失败"}`
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        activateResult
      );
    } catch (error) {
      logger.error("[LicenseController] activate - Error occurred", error);
      return handleErrorResponse(error);
    }
  }
}

LicenseController.toString = () => "[class LicenseController]";
export default LicenseController;
