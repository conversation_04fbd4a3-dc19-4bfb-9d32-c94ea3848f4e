"use strict";

import { deviceConnectService } from "../../service/debug/deviceconnect";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleConnectionError,
  handleErrorResponse,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { deviceTimeService } from "../../service/debug/devicetime";
import { IECReq } from "../../interface/debug/request";

/**
 * 装置基本信息Controller
 * <AUTHOR>
 * @class
 */
class DeviceTimeController {
  constructor() {
    logger.info(
      `[DeviceTimeController] ${t("logs.deviceTimeController.initialized")}`
    );
  }

  /**
   * 获取装置时间
   * @returns {Promise<ApiResponse>}
   */
  async getDeviceTime(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceTimeController] ${t("logs.deviceTimeController.getDeviceTimeStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceTimeController] ${t("logs.deviceTimeController.getDeviceTimeCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceTimeController] ${t("logs.deviceTimeController.getDeviceTimeNotConnected")}`
        );
        return handleConnectionError();
      }

      // 获取装置时间
      logger.debug(
        `[DeviceTimeController] ${t("logs.deviceTimeController.getDeviceTimeConnected")}`
      );
      const devicetime = await deviceTimeService.getDeviceTime(req);

      logger.info(
        `[DeviceTimeController] ${t("logs.deviceTimeController.getDeviceTimeSuccess")}:`,
        devicetime
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        devicetime
      );
    } catch (error) {
      logger.error(
        `[DeviceTimeController] ${t("logs.deviceTimeController.getDeviceTimeException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 设置装置时间
   * @returns {Promise<ApiResponse>}
   */
  async writeDeviceTime(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceTimeController] ${t("logs.deviceTimeController.writeDeviceTimeStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceTimeController] ${t("logs.deviceTimeController.writeDeviceTimeCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceTimeController] ${t("logs.deviceTimeController.writeDeviceTimeNotConnected")}`
        );
        return handleConnectionError();
      }

      // 设置装置时间
      logger.debug(
        `[DeviceTimeController] ${t("logs.deviceTimeController.writeDeviceTimeConnected")}`
      );
      const flag = await deviceTimeService.writeDeviceTime(req);

      logger.info(
        `[DeviceTimeController] ${t("logs.deviceTimeController.writeDeviceTimeSuccess")}:`,
        flag
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      logger.error(
        `[DeviceTimeController] ${t("logs.deviceTimeController.writeDeviceTimeException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }
}

DeviceTimeController.toString = () => "[class DeviceTimeController]";
export default DeviceTimeController;
