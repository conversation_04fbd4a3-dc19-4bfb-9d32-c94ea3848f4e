import { ApiResponse } from "../../data/debug/apiResponse";
import {
  GraphControlRequest,
  GraphGetDataRequest,
  GraphRegisterRequest,
} from "../../interface/hmi/graph";
import { graphViewService } from "../../service/hmi/GraphView";
/**
 * 图符显示
 * <AUTHOR>
 * @version 1.0 2025-03-15
 */
class GraphViewController {
  /**
   * 注册图符数据
   * @returns
   */
  async register(data: GraphRegisterRequest): Promise<ApiResponse> {
    return graphViewService.register(data);
  }

  async getData(requestData: GraphGetDataRequest): Promise<ApiResponse> {
    return graphViewService.getData(requestData);
  }

  async remoteControl(data: GraphControlRequest): Promise<ApiResponse> {
    return graphViewService.remoteControl(data);
  }
  async remoteSet(data: GraphControlRequest): Promise<ApiResponse> {
    return graphViewService.remoteSet(data);
  }
}
module.exports = GraphViewController;
