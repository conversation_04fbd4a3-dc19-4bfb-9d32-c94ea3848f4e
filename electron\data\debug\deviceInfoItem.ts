/**
 * 使用ts定义模型类，包含字段，序号，描述，值
 * <AUTHOR>
 */
export default class DeviceInfoItem {
  // 定义字段并初始化
  private index: number;
  private description: string;
  private value: unknown;

  /**
   * 构造函数
   * @param index 序号
   * @param description 描述
   * @param value 值，这里使用unknown类型以适应不同类型的数据，并在内部进行类型断言
   */
  constructor(index: number, description: string, value: unknown) {
    if (typeof index !== "number" || !Number.isInteger(index)) {
      throw new Error("Index must be an integer");
    }
    if (typeof description !== "string") {
      throw new Error("Description must be a string");
    }

    this.index = index;
    this.description = description;
    this.value = value;
  }

  // Getter 和 Setter 方法
  getIndex(): number {
    return this.index;
  }

  getDescription(): string {
    return this.description;
  }

  getValue(): unknown {
    return this.value;
  }

  setValue(value: unknown): void {
    this.value = value;
  }
}
