      export interface DiffItem {
        name: string;
        description: string;
        minValue: string;
        maxValue: string;
        step: string;
        unit: string;
        inf: string;
        oldValue: string;
        newValue: string;
        grp: string;
        type: string;
        grpName: string;
      }

      export interface AllDiffItem {
        grpname: string;
        data: DiffItem[];
      }