"use strict";

import { deviceConnectService } from "../../service/debug/deviceconnect";
import { paramService } from "../../service/debug/param";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleInternalError,
  handleConnectionError,
  handleCustomResponse,
  handleErrorResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { IECReq } from "../../interface/debug/request";
/**
 * 装置参数定值Controller
 * <AUTHOR>
 * @class
 */
class ParamController {
  constructor() {
    logger.info(`[ParamController] ${t("logs.paramController.initialized")}`);
  }

  // 异步获取装置定值，成功返回ParamItem列表，失败返回ApiResponse
  async getParam(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[ParamController] ${t("logs.paramController.getParamStart")}:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[ParamController] ${t("logs.paramController.getParamNotConnected")}`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[ParamController] ${t("logs.paramController.getParamConnected")}`
      );
      const result = await paramService.getParamInfo(req);

      logger.info(
        `[ParamController] ${t("logs.paramController.getParamSuccess")}: ${result?.length || 0}`
      );
      return new ApiResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[ParamController] ${t("logs.paramController.getParamException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  async getAllParam(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[ParamController] getAllParam - 开始获取所有装置定值，请求参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[ParamController] getAllParam - 设备未连接，无法获取所有装置定值`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[ParamController] getAllParam - 设备已连接，调用服务层获取所有装置定值`
      );
      const result = await paramService.getAllParamInfo(req);

      logger.info(
        `[ParamController] getAllParam - 成功获取所有装置定值，结果数量: ${result?.length || 0}`
      );
      return new ApiResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[ParamController] getAllParam - 获取所有装置定值异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  // 异步修改装置定值，成功返回true，失败返回ApiResponse
  async confirmParam(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[ParamController] confirmParam - 开始修改装置定值，请求参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[ParamController] confirmParam - 设备未连接，无法修改装置定值`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[ParamController] confirmParam - 设备已连接，调用服务层修改装置定值`
      );
      const flag = await paramService.modifyParam(req);

      logger.info(
        `[ParamController] confirmParam - 修改装置定值成功，结果:`,
        flag
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      logger.error(`[ParamController] confirmParam - 修改装置定值异常:`, error);
      return handleErrorResponse(error);
    }
  }

  // 导入前比较数据
  async getDiffParam(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[ParamController] getDiffParam - 开始获取定值差异，请求参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[ParamController] getDiffParam - 设备未连接，无法获取定值差异`
      );
      return handleConnectionError();
    }

    const { path } = req.data;
    logger.debug(`[ParamController] getDiffParam - 导入路径: ${path}`);

    // 检查导出路径是否为空
    if (!path) {
      logger.error(
        `[ParamController] ${t("logs.paramController.getDiffParamEmptyPath")}`
      );
      return handleCustomResponse(
        ERROR_CODES.INVALID_PARAM,
        t("errors.importPathEmpty")
      );
    }

    try {
      logger.debug(
        `[ParamController] getDiffParam - 设备已连接，调用服务层获取定值差异`
      );
      const { data, msg } = await paramService.getDiffParam(req);

      logger.info(
        `[ParamController] getDiffParam - 成功获取定值差异，结果:`,
        data
      );
      return handleCustomResponse(ERROR_CODES.SUCCESS, msg, data);
    } catch (error) {
      logger.error(`[ParamController] getDiffParam - 获取定值差异异常:`, error);
      return handleErrorResponse(error);
    }
  }

  // 导入前比较数据
  async getAllDiffParam(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[ParamController] getAllDiffParam - 开始获取所有定值差异，请求参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[ParamController] getAllDiffParam - 设备未连接，无法获取所有定值差异`
      );
      return handleConnectionError();
    }

    const { path } = req.data;
    logger.debug(`[ParamController] getAllDiffParam - 导入路径: ${path}`);

    // 检查导出路径是否为空
    if (!path) {
      logger.error(
        `[ParamController] ${t("logs.paramController.getAllDiffParamEmptyPath")}`
      );
      return handleCustomResponse(
        ERROR_CODES.INVALID_PARAM,
        t("errors.importPathEmpty")
      );
    }

    try {
      logger.debug(
        `[ParamController] getAllDiffParam - 设备已连接，调用服务层获取所有定值差异`
      );
      const { data, msg } = await paramService.getAllDiffParam(req);

      logger.info(
        `[ParamController] getAllDiffParam - 成功获取所有定值差异，结果:`,
        data
      );
      return handleCustomResponse(ERROR_CODES.SUCCESS, msg, data);
    } catch (error) {
      logger.error(
        `[ParamController] getAllDiffParam - 获取所有定值差异异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  // 异步导入装置定值，成功返回true，失败返回ApiResponse
  async importParam(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[ParamController] importParam - 开始导入装置定值，请求参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[ParamController] importParam - 设备未连接，无法导入装置定值`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[ParamController] importParam - 设备已连接，调用服务层导入装置定值`
      );
      const flag = await paramService.importParam(req);

      logger.info(
        `[ParamController] importParam - 导入装置定值成功，结果:`,
        flag
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      logger.error(`[ParamController] importParam - 导入装置定值异常:`, error);
      return handleErrorResponse(error);
    }
  }

  // 异步导出装置定值，成功返回true，失败返回ApiResponse
  async exportParam(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[ParamController] exportParam - 开始导出装置定值，请求参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[ParamController] exportParam - 设备未连接，无法导出装置定值`
      );
      return handleConnectionError();
    }

    const { path } = req.data;
    logger.debug(`[ParamController] exportParam - 导出路径: ${path}`);

    // 检查导出路径是否为空
    if (!path) {
      logger.error(
        `[ParamController] ${t("logs.paramController.exportParamEmptyPath")}`
      );
      return handleCustomResponse(
        ERROR_CODES.INVALID_PARAM,
        t("errors.exportPathEmpty")
      );
    }

    try {
      logger.debug(
        `[ParamController] exportParam - 设备已连接，调用服务层导出装置定值`
      );
      const flag = await paramService.exportParam(req);

      logger.info(
        `[ParamController] exportParam - 导出装置定值成功，结果:`,
        flag
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      logger.error(`[ParamController] exportParam - 导出装置定值异常:`, error);
      return handleInternalError();
    }
  }

  async exportAllParam(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[ParamController] exportAllParam - 开始导出所有装置定值，请求参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[ParamController] exportAllParam - 设备未连接，无法导出所有装置定值`
      );
      return handleConnectionError();
    }

    const { path } = req.data;
    logger.debug(`[ParamController] exportAllParam - 导出路径: ${path}`);

    // 检查导出路径是否为空
    if (!path) {
      logger.error(
        `[ParamController] ${t("logs.paramController.exportAllParamEmptyPath")}`
      );
      return handleCustomResponse(
        ERROR_CODES.INVALID_PARAM,
        t("errors.exportPathEmpty")
      );
    }

    try {
      logger.debug(
        `[ParamController] exportAllParam - 设备已连接，调用服务层导出所有装置定值`
      );
      const flag = await paramService.exportAllParam(req);

      logger.info(
        `[ParamController] exportAllParam - 导出所有装置定值成功，结果:`,
        flag
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      logger.error(
        `[ParamController] exportAllParam - 导出所有装置定值异常:`,
        error
      );
      return handleInternalError();
    }
  }

  /** 获取当前运行区 */
  async getCurrentRunArea(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[ParamController] getCurrentRunArea - 开始获取当前运行区，请求参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[ParamController] getCurrentRunArea - 设备未连接，无法获取当前运行区`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[ParamController] getCurrentRunArea - 设备已连接，调用服务层获取当前运行区`
      );
      const result = await paramService.getCurrentRunArea(req);

      logger.info(
        `[ParamController] getCurrentRunArea - 成功获取当前运行区，结果:`,
        result
      );
      return result;
    } catch (error) {
      logger.error(
        `[ParamController] getCurrentRunArea - 获取当前运行区异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /** 选择定值区 */
  async selectRunArea(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[ParamController] selectRunArea - 开始选择定值区，请求参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[ParamController] selectRunArea - 设备未连接，无法选择定值区`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[ParamController] selectRunArea - 设备已连接，调用服务层选择定值区`
      );
      const result = await paramService.selectRunArea(req);

      logger.info(
        `[ParamController] selectRunArea - 成功选择定值区，结果:`,
        result
      );
      return new ApiResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(`[ParamController] selectRunArea - 选择定值区异常:`, error);
      return handleErrorResponse(error);
    }
  }
}

ParamController.toString = () => "[class ParamController]";
export default ParamController;
