import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleCustomResponse,
  handleInternalError,
} from "../../data/debug/errorCodes";
import { CustomInfoService } from "../../service/debug/custominfo";
import { IECReq } from "../../interface/debug/request";
import { ApiResponse } from "../../data/debug/apiResponse";
import { logger } from "ee-core/log";

const service = new CustomInfoService();

class CustomInfoController {
  /** 获取所有自定义组和报告 */
  async getAllGroups(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const deviceId = req.head.id;
      logger.info(`[CustomInfoController][getAllGroups] deviceId:`, deviceId);
      const data = await service.getAllGroups(deviceId);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        data
      );
    } catch (e: any) {
      console.error(`[CustomInfoController][getAllGroups] error:`, e);
      return handleInternalError(e?.message || String(e));
    }
  }

  /** 新增菜单 */
  async addMenu(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const deviceId = req.head.id;
      const { group } = req.data;
      logger.info(
        `[CustomInfoController][addMenu] deviceId:`,
        deviceId,
        "group:",
        group
      );
      await service.addMenu(deviceId, group);
      return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS);
    } catch (e: any) {
      console.error(`[CustomInfoController][addMenu] error:`, e);
      return handleInternalError(e?.message || String(e));
    }
  }

  /** 编辑菜单 */
  async editMenu(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const deviceId = req.head.id;
      const { uuid, newGroup } = req.data;
      logger.info(
        `[CustomInfoController][editMenu] deviceId:`,
        deviceId,
        "uuid:",
        uuid,
        "newGroup:",
        newGroup
      );
      await service.editMenu(deviceId, uuid, newGroup);
      return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS);
    } catch (e: any) {
      console.error(`[CustomInfoController][editMenu] error:`, e);
      return handleInternalError(e?.message || String(e));
    }
  }

  /** 删除菜单 */
  async deleteMenu(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const deviceId = req.head.id;
      const { uuid } = req.data;
      logger.info(
        `[CustomInfoController][deleteMenu] deviceId:`,
        deviceId,
        "uuid:",
        uuid
      );
      await service.deleteMenu(deviceId, uuid);
      return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS);
    } catch (e: any) {
      console.error(`[CustomInfoController][deleteMenu] error:`, e);
      return handleInternalError(e?.message || String(e));
    }
  }

  /** 新增报告 */
  async addReport(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const deviceId = req.head.id;
      const { groupUuid, report } = req.data;
      logger.info(
        `[CustomInfoController][addReport] deviceId:`,
        deviceId,
        "groupUuid:",
        groupUuid,
        "report:",
        report,
        "newname:",
        report?.newname
      );
      await service.addReport(deviceId, groupUuid, report);
      return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS);
    } catch (e: any) {
      console.error(`[CustomInfoController][addReport] error:`, e);
      return handleInternalError(e?.message || String(e));
    }
  }

  /** 编辑报告 */
  async editReport(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const deviceId = req.head.id;
      const { groupUuid, reportUuid, newReport } = req.data;
      logger.info(
        `[CustomInfoController][editReport] deviceId:`,
        deviceId,
        "groupUuid:",
        groupUuid,
        "reportUuid:",
        reportUuid,
        "newReport:",
        newReport,
        "newname:",
        newReport?.newname
      );
      await service.editReport(deviceId, groupUuid, reportUuid, newReport);
      return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS);
    } catch (e: any) {
      console.error(`[CustomInfoController][editReport] error:`, e);
      return handleInternalError(e?.message || String(e));
    }
  }

  /** 删除报告 */
  async deleteReport(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const deviceId = req.head.id;
      const { groupUuid, reportUuid } = req.data;
      logger.info(
        `[CustomInfoController][deleteReport] deviceId:`,
        deviceId,
        "groupUuid:",
        groupUuid,
        "reportUuid:",
        reportUuid
      );
      await service.deleteReport(deviceId, groupUuid, reportUuid);
      return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS);
    } catch (e: any) {
      console.error(`[CustomInfoController][deleteReport] error:`, e);
      return handleInternalError(e?.message || String(e));
    }
  }

  /** 获取可继承报告（LG报告） */
  async getLGReports(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const deviceId = req.head.id;
      logger.info(`[CustomInfoController][getLGReports] deviceId:`, deviceId);
      const data = await service.getLGReports(deviceId);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        data
      );
    } catch (e: any) {
      console.error(`[CustomInfoController][getLGReports] error:`, e);
      return handleInternalError(e?.message || String(e));
    }
  }

  /** 获取所有 fc 列表 */
  async getFcList(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const deviceId = req.head.id;
      logger.info(`[CustomInfoController][getFcList] deviceId:`, deviceId);
      const data = await service.getFcList(deviceId);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        data
      );
    } catch (e: any) {
      console.error(`[CustomInfoController][getFcList] error:`, e);
      return handleInternalError(e?.message || String(e));
    }
  }

  /** 验证菜单名称唯一性 */
  async validateMenuName(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const deviceId = req.head.id;
      const { name } = req.data;
      logger.info(
        `[CustomInfoController][validateMenuName] deviceId:`,
        deviceId,
        "name:",
        name
      );
      const data = await service.validateMenuName(deviceId, name);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        data
      );
    } catch (e: any) {
      console.error(`[CustomInfoController][validateMenuName] error:`, e);
      return handleInternalError(e?.message || String(e));
    }
  }

  /** 根据 fc 获取菜单及点 */
  async getMenusByFc(req: IECReq<any>): Promise<ApiResponse> {
    try {
      const deviceId = req.head.id;
      const { fc } = req.data;
      logger.info(
        `[CustomInfoController][getMenusByFc] deviceId:`,
        deviceId,
        "fc:",
        fc
      );
      const data = await service.getMenusByFc(deviceId, fc);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        data
      );
    } catch (e: any) {
      console.error(`[CustomInfoController][getMenusByFc] error:`, e);
      return handleInternalError(e?.message || String(e));
    }
  }
}

module.exports = CustomInfoController;
