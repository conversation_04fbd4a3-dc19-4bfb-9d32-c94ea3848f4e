export interface IECNotify {
  type:
    | "connect"
    | "disconnect"
    | "error"
    | "readCommonReport"
    | "readGroupReport"
    | "readAuditLogReport"
    | "readOperateReport"
    | "readTripReport"
    | "fileUpload"
    | "fileDownload"
    | "matrixDownload"
    | "fileUploadError"
    | "fileUploadSuccess"
    | "fileDecrypt"
    | "upadRpcMessage";
  data: unknown;
  /** 附带装置ID，便于前端多装置过滤 */
  deviceId?: string;
  /** 标识是否为部分数据（用于分批回调） */
  isPartial?: boolean;
}

export const IEC_EVENT = {
  NOTIFY: "notify",
  REPORT_NOTIFY: "report_notify",
  FILEUPLOAD_NOTIFY: "fileupload_notify",
  FILEDOWNLOAD_NOTIFY: "filedownload_notify",
  MATRIX_NOTIFY: "matrix_notify",
  CONNECT_DEVICE: "connect_device",
  READ_GROUP_TITLE: "read_group_title",
  READ_ALL_ENTRY: "read_all_entry",
  FILE_DECRYPT_NOTIFY: "file_decrypt_notify",
  BACKUP_NOTIFY: "backup_notify",
};