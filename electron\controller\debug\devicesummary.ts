"use strict";
import { deviceConnectService } from "../../service/debug/deviceconnect";
import { deviceSummaryService } from "../../service/debug/devicesummary";

import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleErrorResponse,
  handleConnectionError,
  handleCustomResponse,
  handleInternalError,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { IECReq } from "../../interface/debug/request";

/**
 * 装置信息Controller
 *
 *
 * <AUTHOR>
 * @class
 */
class DeviceSummaryController {
  constructor() {
    logger.info(
      `[DeviceSummaryController] ${t("logs.deviceSummaryController.initialized")}`
    );
  }

  /**
   * 获取SG数量
   * @param req 请求参数
   * @returns ApiResponse
   */
  public async getSGCount(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceSummaryController] ${t("logs.deviceSummaryController.getSGCountStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceSummaryController] ${t("logs.deviceSummaryController.getSGCountCheckConnection")}: ${req.head.id}`
      );
      const connected = await deviceConnectService.isConnected(req.head.id);

      if (!connected) {
        logger.warn(
          `[DeviceSummaryController] ${t("logs.deviceSummaryController.getSGCountNotConnected")}: ${req.head.id}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceSummaryController] ${t("logs.deviceSummaryController.getSGCountConnected")}`
      );
      const sgCount = await deviceSummaryService.getSGCount(req);

      logger.info(
        `[DeviceSummaryController] ${t("logs.deviceSummaryController.getSGCountSuccess")}:`,
        sgCount
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        sgCount
      );
    } catch (error) {
      logger.error(
        `[DeviceSummaryController] ${t("logs.deviceSummaryController.getSGCountException")}: ${req.head.id}`,
        error
      );
      return handleInternalError();
    }
  }

  /**
   * 获取装置汇总信息
   * getDeviceInfo
   */
  async getSummaryInfo(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceSummaryController] getSummaryInfo - 开始获取装置汇总信息，请求参数:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceSummaryController] getSummaryInfo - 检查设备连接状态`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceSummaryController] getSummaryInfo - 设备未连接，无法获取装置汇总信息`
        );
        return handleConnectionError();
      }

      // 返回列表
      logger.debug(
        `[DeviceSummaryController] getSummaryInfo - 设备已连接，调用服务层获取装置汇总信息`
      );
      const result = await deviceSummaryService.getSummaryInfo(req);

      logger.info(
        `[DeviceSummaryController] getSummaryInfo - 成功获取装置汇总信息，结果:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceSummaryController] getSummaryInfo - 获取装置汇总信息异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 获取参数定值汇总信息
   * @param req
   * @returns
   */
  async getParamSummary(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceSummaryController] getParamSummary - 开始获取参数定值汇总信息，请求参数:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceSummaryController] getParamSummary - 检查设备连接状态`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceSummaryController] getParamSummary - 设备未连接，无法获取参数定值汇总信息`
        );
        return handleConnectionError();
      }

      // 返回列表
      logger.debug(
        `[DeviceSummaryController] getParamSummary - 设备已连接，调用服务层获取参数定值汇总信息`
      );
      const result = await deviceSummaryService.getParamSummary(req);

      logger.info(
        `[DeviceSummaryController] getParamSummary - 成功获取参数定值汇总信息，结果:`,
        "success"
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceSummaryController] getParamSummary - 获取参数定值汇总信息异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }
}

DeviceSummaryController.toString = () => "[class DeviceSummaryController]";
export default DeviceSummaryController;
