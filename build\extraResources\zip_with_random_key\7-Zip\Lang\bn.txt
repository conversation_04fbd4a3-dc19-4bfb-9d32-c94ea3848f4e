﻿;!@Lang2@!UTF-8!
;  4.46 : Team <PERSON> (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
;
;
;
;
;
;
;
;
;
;
0
7-Zip
Bangla
বাংলা
401
ঠিক আছে
বাতিল



&হ্যাঁ
&না
&বন্ধ করা
সাহায্য

&চালিয়ে যাওয়া
440
&সবগুলোর জন্য হ্যাঁ
স&বগুলোর জন্য না
বন্ধ
আবার শুরু
&পটভূমি
& সামনে
&বিরতি
বিরতিতে অবস্থানরত
আপনি বাতিল করতে ইচ্ছুক?
500
&ফাইল
&পরিবর্তন
প্রদর্শন&
&প্রিয়
&দরকারি
&সহায়তা
540
&উন্মুক্ত করা
7-zip-এ উন্মুক্ত করা
বাহিরে উন্মুক্ত করা
&প্রদর্শন
&পরিবর্তন
নাম পরিবর্তন
&অনুলিপি হবে...
প্রতিস্থাপন হবে...
&মুছে ফেলা
&ফাইল খন্ডায়ন...
ফাইল সংযোজন...
বৈশিষ্টাবলি
মন্তব্য
Check<PERSON><PERSON>না করা

ফোল্ডার সৃষ্টি
ফাইল সৃষ্টি
বাহির
600
সব নির্বাচন
নির্বাচন রদ করা
উল্টো নির্বাচন
নির্বাচন...
নির্বাচন রদ করা...
ধরণ অনুযায়ী নির্বাচন
ধরণ অনুযায়ী নির্বাচন রদ করা
700
বৃহৎ প্রতিক
ছোট্ট প্রতিক
&তালিকা
&বিবরণ
730
অসজ্জিত
সমতল সজ্জা
&ব্যবস্থাপক দ্বিখন্ডন
&দরকারিখুটি
মূল ফোল্ডার উন্মুক্ত করা
এক পর্যায় উপরে ...
ফোল্ডারের অতীত বিবরণ...
&সতেজতা
750
সংকোচোন ব্যবস্থাপক খুটি
সাধারণ খুটি
বৃহৎ বোতাম
বোতামের শিরোনাম প্রদর্শন
800
&প্রিয় ফোল্ডার হিসাবে সংযোজন ...
পছন্দের তালিকা
900
&পছন্দগুলো...
&বেঞ্চমার্ক
960
&সাহায্য...
&7-Zip সর্ম্পকে...
1003
অবস্থান
নাম
পরিচয়
ফোল্ডার
আকার
সংকুচিত আকার
বৈশিষ্ট
সৃষ্টি হয়েছে
ব্যবহার হয়েছে
পরিবর্ধন হয়েছে
দৃঢ়
Commented
আটকানো
খন্ডনের পূর্বে
খন্ডনের পরে
অভিধান
CRC
ধরন
বিরোধী
পদ্ধতি
চলতি অপারেটিং সিস্টেম
ফাইল ব্যবস্থা
ব্যবহারকারী
দল
বাধা
মন্তব্য
অবস্থান
পথের বিশেষায়ণ (Path Prefix)
























ত্রুটি
সম্পূর্ণ আকার
অবশিষ্ট জায়গা
ক্লাস্টারের আকার
শিরোনাম
স্থানীয় নাম
বিতরণকারী
2100
পছন্দগুলো
ভাষা
ভাষা:
সম্পাদক
বর্তমান সম্পাদক :

2200
বর্তমান অবস্থা
7-Zip-এর সাথে সম্পর্কিত :
2301
সাহায্যকারী তালিকায় 7-Zip সংযোজন
সাহায্যকারী তালিকায় একের ভিতর সব গুটিয়ে ফেলা
সাহায্যকারী তালিকার বিষয়সমূহ:
2320
<ফোল্ডার>
<সংকুচিত ফাইল>
সংকুচিত ফাইল চালু করা
ফাইল সম্প্রসারণ...
সংকুচিত ফাইলে সংযোজন...
সংকুচিত ফাইল নিরীক্ষণ
এখানেই সম্প্রসারণ
সম্প্রসারণ করা হবে {0}
সযোজন করা হবে {0}
সংকোচন এবং ই-মেইল...
সংকোচন -  {0} এবং ই-মেইল
2400
ফোল্ডার
&কার্যরত ফোল্ডার
&অস্থায়ী ফোল্ডার
&প্রচলিত
&নির্দিষ্ট:
অস্থায়ী অংশের জন্য ব্যবহার করা
অস্থায়ী ফোল্ডার নির্বাচন করুন।
2500
পছন্দগুলো
".." ফাইল প্রদর্শন
ফাইলের আসল প্রতিক দেখানো
কম্পিউটার চালকের তালিকা দেখানো
পূর্ণ পর্যায় প্রদর্শন
ছকের লাইন প্রদর্শন

পরিপূরক নিবাচনের পদ্ধতি
বেশি স্মৃতির ব্যবহার
2900
7-Zip সম্পর্কে
7-Zip একটি মুক্ত প্রোগ্রাম কিন্তু এটি 7-Zip এর কতৃপক্ষের কাচে নিবন্ধনের মাধ্যমে আপনি উন্নত সেবা পেতে পারেন
3000

কোন ত্রুটি নেই
{0} ফাইল(সমূহ) নির্বাচিত
'{0}' ফোল্ডার সৃষ্টি করা সম্ভব হচ্ছেনা
এই সংকোচনের ক্ষেত্রে এই সেবা প্রদান করা সম্ভব হচ্ছে না।
'{0}' -কে সংকুচিত ফাইল হিসেবে চালু করা সম্ভব হচ্ছেনা
'{0}' বদ্ধ সংকুচিত ফাইল চালু করা সম্ভব হচ্ছেনা. ভুল পাসওয়ার্ড?


ফাইলটি '{0}' পরিমার্জিত.\nআপনি সংকুচিত ফাইলটি ও পরিমার্জন করতে চান?
পরিমার্জন করা সম্ভব হয়নি\n'{0}'
সম্পাদক চালু করা সম্ভব নয়




অনেক বেশী ফাইল
3300
সম্প্রসারণ করা হচ্ছে
সংকোচায়ন প্রক্রিয়াধীন
নিরক্ষণ করছে ...
উন্মুক্ত করা হচ্ছে...
তথ্য সংগ্রহ চলছে... (Scanning...)
3400
সম্প্রসারণ
&সম্প্রসারণ করা হবে:
ফাইল সম্প্রসারনের ঠিকানা
3410
ঠিকানা নির্বাচন পদ্ধতি
পূর্ণ ঠিকানাসমূহ
ঠিকানাবিহীন
3420
প্রতিস্থাপন পদ্ধতি
প্রতিস্থাপনের পূর্বাভাস
আভাসবিহীন প্রতিস্থাপন
একই পরিচয় প্রাপ্ত ফাইল এড়িয়ে চলা
স্বয়ংক্রিয় পুঃনামকরণ
একই পরিচয় প্রাপ্ত ফাইলের নাম পরিবর্ত্ন
3500
ফাইল প্রতিস্থাপন নিশ্চিত করণ
নির্ধারিত ফোল্ডারে ফাইলটি আগেথেকেই আছে
আপনিকি বর্তমান ফাইলটি প্রতিস্থাপন করতে চান?
এইটির সাথে?
{0} bytesবাইট
স্বয়ংক্রিয় পুঃনামকরণ
3700
অসমর্থিত সংকোচন পদ্ধতি -'{0}'.
'{0}' ফাইলে ত্রুটিপূর্ণ তথ্য. ফাইলটি খন্ডিত
'{0}' ফাইলে CRC ব্যর্থ. ফাইলটি খন্ডিত
'{0}' বদ্ধ ফাইলে তথ্যে ত্রুটি. ভুল পাসওয়ার্ড?
'{0}' বদ্ধ ফাইলে CRC ব্যর্থ.  ভুল পাসওয়ার্ড?
3800
পাসওয়ার্ডটি প্রবেশ করুনঃ
পাসওয়ার্ডটি প্রবেশ করুনঃ
আবার পাসওয়ার্ড প্রবেশ করুনঃ
&পাসওয়ার্ড প্রদর্শন
পাসওয়ার্ড দুটি একই নয়
শুধু ইংলিশ বর্ণ, সংখ্যা এবং বিশেষ বর্ণ (!, #, $, ...) পাসওয়ার্ড হিসেবে ব্যবহার করুন
পাসওয়ার্ডটি খুব বেশী বড় হয়েছে
পাসওর্য়াড
3900
অতিবাহিত সময়ঃ
সময় বাকি আছেঃ
আকার:
গতি:


বিফলতা :

4000
সংকোচনে সংযোজন
&সংকোচন
&পরিমার্জন পদ্ধতি:
সংকোচনের & পরিচয়:
সংকোচনের &পর্যায়:
সংকোচন &পদ্ধতি:
&Dictionary size:
&Word size:
Solid block size:
CPU-এর thread-এর সংখ্যা:
&Parameters:
পছন্দনীয়
স্বয়ংক্রিয় সংকোচন প্রোগ্রাম তৈরি
বিনিময়যোগ্য ফাইল সংকোচন
বদ্ধ করা
বদ্ধ করার পদ্ধতি:
ফাইলের নাম &আটকে ফেলা
সংকোচনের জন্য স্মৃতির ব্যবহার:
সম্প্রসারনের জন্য স্মৃতির ব্যবহার:
4050
অতি সংকোচায়ন
অতি দ্রুত
দ্রুত
সাধারন
সর্ব্বোচ্চ
পলকের গতি
4060
সংকোচন ও ফাইল প্রতিস্থাপন
পরিমার্জন ও ফাইল প্রতিস্থাপন
উল্লেখিত ফাইলে সতেজতা প্রদান
ফাইল সাজিয়ে রাখা
4070
বিচরণ
সকল ফাইল
Non-solid
Solid
6000
অনুলিপি গ্রহন
অনুলিপি গ্রহন এবং মুছে ফেলা
অনুলিপি করা হবে:
প্রতিস্থাপিত হবে:
অনুলিপি করা হচ্ছে...
প্রতিস্থাপিত হচ্ছে...
নাম পরিবর্তন...
গন্তব্য ফোল্ডার নির্বাচন.
কার্যটি সম্ভব নয়
ফাইল বা ফোল্ডারের নাম পরিবর্তনে সম্ভব নয়
ফাইল অনুলিপি নিশ্চিতকরণ
আপনি কি ফাইলগুলোকে সংকুচিত ফাইলে অনুলিপি গ্রহণ করতে চান।
6100
ফাইলটি মুছে ফেলতে কি আপনি নিশ্চিত
ফোল্ডারটি মুছে ফেলতে কি আপনি নিশ্চিত
ফাইলটি মুছে ফেলতে কি আপনি নিশ্চিত
মুছে ফেলতে আপনি কি নিশ্চিত - '{0}'?
'{0}' ফোল্ডার এবং এর সব ফাইল আপনি কি মুছে ফেলতে নিশ্চিত?
নির্বাচিত {0} টি ফাইল আপনি কি মুছে ফেলতে নিশ্চিত?
মুছে ফেলা হচ্ছে...
ফাইল বা ফোল্ডার মুছে ফেলাতে সমস্যা হচ্ছে

6300
ফোল্ডার সৃষ্টি
ফাইল সৃষ্টি
ফোল্ডারের নাম:
ফাইল নাম:
নতুন ফোল্ডার
নতুন ধারক
ফোল্ডার সৃষ্টিতে সমস্যা
ফাইল সৃষ্টিতে সমস্যা
6400
মন্তব্য
&মন্তব্য:
নির্বাচন
নির্বাচন রদ করা
আড়াল করা:
6600

ফোল্ডারের অতিত বিবরন
সমস্যা নিরাময় আভাস
আভাস
7100
কম্পিউটার
আন্তঃ সম্পর্ক

কম্পিউটার চালক
7200
সংজোযন
সম্প্রসারন
নিরীক্ষণ
অনুলিপি গ্রহন
প্রতিস্থাপন
মুছে ফেলা
তথ্য
7300
ফাইল খন্ডায়ন
&ফাইল খন্ডায়িত হবে:
volumes(খন্ডে), bytes(বাইটস)-এ খন্ডায়নঃ
ফাইল খন্ডায়ন চলছে...
ফাইল খন্ডায়ন নিশ্চিতকরণ
আপনি কি সংকুচিত ফাইলটিকে {0} খন্ডে খন্ডায়ন করতে চান?
খন্ডের আকার অবশ্যই মূল ফাইলের চেয়ে ছোট হতে হবে
খন্ডের আকারে ভুল
উল্লেক্ষিত খন্ডের আকার : {0} bytes.\nআপনি কি সংকোচিত ফাইলটিকে এ ভাবেই খন্ডে খন্ডায়ন করতে চান?
7400
ফাইল একীভূতি করণ
&একীভূতি করা হবে:
একীভূতি চলছে...
শুধু প্রথম ফাইলটি নির্বাচন করুন


7500
Checksum গননা চলছে...
Checksum তথ্য
তথ্যের জন্য CRC checksum:
তথ্য এবং নামের জন্য CRC checksum:
7600
বেঞ্চমার্ক
ব্যবহৃত স্মৃতি :
সংকোচায়ন ...
সম্প্রসারণ ...
রেটিং
মোট রেটিং
চলতি
ফলাফল
CPU ব্যবহার করছে
Rating / ব্যবহার করছে
সফলতা :
